// Device Types
export type DeviceType = string; // Now supports custom device types

// Fault Priority
export type FaultPriority = "عاجل" | "متوسط" | "عادي";

// Fault Status
export type FaultStatus = "مفتوح" | "قيد المعالجة" | "تم الإصلاح" | "لم يتم الإصلاح";

// Engineer Status
export type EngineerStatus = "متاح" | "مشغول" | "في إجازة";

// Engineer Specialty
export type EngineerSpecialty = "أجهزة ليزر" | "أجهزة تخسيس" | "عام";

// Attachment interface
export interface Attachment {
  name: string;
  type: string;
  size: number;
}

// Video Attachment interface
export interface VideoAttachment {
  id: string;
  name: string;
  url: string;
  type: 'before' | 'after';
  uploadedAt: string;
  size: number;
}

// Fault interface
export interface Fault {
  id: string;
  customerName: string;
  customerPhone: string;
  customerEmail: string;
  facilityAddress: string;
  deviceType: DeviceType;
  serialNumber?: string;
  faultDescription: string;
  priority: FaultPriority;
  status: FaultStatus;
  engineer?: string;
  reportedAt: string;
  completedAt?: string;
  attachments?: Attachment[];
  videosBeforeMaintenance?: VideoAttachment[];
  videosAfterMaintenance?: VideoAttachment[];
  notes?: string;
}

// Engineer interface
export interface Engineer {
  id: string;
  name: string;
  phone: string;
  email: string;
  specialty: EngineerSpecialty;
  status: EngineerStatus;
  area: string;
  joinedAt: string;
}

// Report interfaces
export interface ReportData {
  totalFaults: number;
  completedFaults: number;
  pendingFaults: number;
  urgentFaults: number;
  engineerPerformance: {
    engineerName: string;
    completedFaults: number;
    pendingFaults: number;
  }[];
  faultsByPriority: {
    priority: FaultPriority;
    count: number;
  }[];
  faultsByStatus: {
    status: FaultStatus;
    count: number;
  }[];
}

// CSV Report interface
export interface CSVReportData {
  id: string;
  fileName: string;
  uploadedAt: string;
  data: any[];
  summary: {
    totalRows: number;
    columns: string[];
  };
}
