@echo off
chcp 65001 >nul
title نظام إدارة الصيانة - Maintenance Management System

echo.
echo ========================================
echo    نظام إدارة الصيانة
echo    Maintenance Management System
echo ========================================
echo.

echo [1/4] التحقق من Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: Node.js غير مثبت!
    echo.
    echo يرجى تثبيت Node.js من الرابط التالي:
    echo https://nodejs.org
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js مثبت بنجاح
node --version

echo.
echo [2/4] التحقق من npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: npm غير متوفر!
    pause
    exit /b 1
)

echo ✅ npm متوفر
npm --version

echo.
echo [3/4] تثبيت التبعيات...
echo هذا قد يستغرق بضع دقائق في المرة الأولى...

if not exist "node_modules" (
    echo تثبيت التبعيات للمرة الأولى...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ خطأ في تثبيت التبعيات!
        echo.
        echo جرب الحلول التالية:
        echo 1. تأكد من اتصال الإنترنت
        echo 2. شغل الأمر كمدير: npm cache clean --force
        echo 3. احذف مجلد node_modules وأعد المحاولة
        echo.
        pause
        exit /b 1
    )
) else (
    echo ✅ التبعيات مثبتة مسبقاً
)

echo.
echo [4/4] تشغيل التطبيق...
echo.
echo ========================================
echo التطبيق جاهز! سيفتح في المتصفح تلقائياً
echo الرابط: http://localhost:8080
echo.
echo لإيقاف التطبيق: اضغط Ctrl+C
echo ========================================
echo.

timeout /t 3 /nobreak >nul

start http://localhost:8080

npm run dev

pause
