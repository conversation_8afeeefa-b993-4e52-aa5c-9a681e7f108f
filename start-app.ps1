# Fault Management System Startup Script
Write-Host "Starting Fault Management System..." -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green

# Set the execution policy for this session
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force

# Change to the script directory
Set-Location $PSScriptRoot

# Function to check if a command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Check if Node.js is installed
if (-not (Test-Command "node")) {
    Write-Host "Error: Node.js is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Node.js from https://nodejs.org/" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if npm is installed
if (-not (Test-Command "npm")) {
    Write-Host "Error: npm is not installed or not in PATH" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Node.js and npm are available" -ForegroundColor Green

# Try to start the development server
try {
    Write-Host "Starting development server..." -ForegroundColor Yellow
    
    # Method 1: npm run dev
    Write-Host "Attempting: npm run dev" -ForegroundColor Cyan
    npm run dev
}
catch {
    Write-Host "npm run dev failed, trying alternative methods..." -ForegroundColor Yellow
    
    try {
        # Method 2: npx vite
        Write-Host "Attempting: npx vite" -ForegroundColor Cyan
        npx vite
    }
    catch {
        try {
            # Method 3: Direct vite execution
            Write-Host "Attempting: Direct vite execution" -ForegroundColor Cyan
            & ".\node_modules\.bin\vite.cmd"
        }
        catch {
            Write-Host "All startup methods failed!" -ForegroundColor Red
            Write-Host "Please check your Node.js installation and try running 'npm install' first" -ForegroundColor Yellow
            Read-Host "Press Enter to exit"
            exit 1
        }
    }
}
