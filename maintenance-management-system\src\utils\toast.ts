// Simple toast utility functions
export const showSuccess = (message: string) => {
  // In a real app, you would use a toast library like react-hot-toast
  console.log('✅ Success:', message);
  alert(`✅ ${message}`);
};

export const showError = (message: string) => {
  console.log('❌ Error:', message);
  alert(`❌ ${message}`);
};

export const showInfo = (message: string) => {
  console.log('ℹ️ Info:', message);
  alert(`ℹ️ ${message}`);
};

export const showWarning = (message: string) => {
  console.log('⚠️ Warning:', message);
  alert(`⚠️ ${message}`);
};
