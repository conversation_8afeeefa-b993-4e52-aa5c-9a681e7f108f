import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { FileText, Download, BarChart3, <PERSON><PERSON><PERSON>, TrendingUp, Users, Wrench, Calendar } from "lucide-react";
import { useData } from "@/context/DataContext";

const Reports = () => {
  const { faults, engineers } = useData();

  // إحصائيات عامة
  const totalFaults = faults.length;
  const completedFaults = faults.filter(f => f.status === 'تم الإصلاح').length;
  const inProgressFaults = faults.filter(f => f.status === 'قيد المعالجة').length;
  const openFaults = faults.filter(f => f.status === 'مفتوح').length;
  const urgentFaults = faults.filter(f => f.priority === 'عاجل' && f.status !== 'تم الإصلاح').length;

  // إحصائيات المهندسين
  const totalEngineers = engineers.length;
  const availableEngineers = engineers.filter(e => e.status === 'متاح').length;
  const busyEngineers = engineers.filter(e => e.status === 'مشغول').length;

  // إحصائيات حسب الأولوية
  const priorityStats = [
    { priority: 'عاجل', count: faults.filter(f => f.priority === 'عاجل').length, color: 'bg-red-100 text-red-800' },
    { priority: 'متوسط', count: faults.filter(f => f.priority === 'متوسط').length, color: 'bg-yellow-100 text-yellow-800' },
    { priority: 'عادي', count: faults.filter(f => f.priority === 'عادي').length, color: 'bg-green-100 text-green-800' },
  ];

  // إحصائيات حسب الحالة
  const statusStats = [
    { status: 'مفتوح', count: openFaults, color: 'bg-yellow-100 text-yellow-800' },
    { status: 'قيد المعالجة', count: inProgressFaults, color: 'bg-blue-100 text-blue-800' },
    { status: 'تم الإصلاح', count: completedFaults, color: 'bg-green-100 text-green-800' },
  ];

  // أداء المهندسين
  const engineerPerformance = engineers.map(engineer => {
    const engineerFaults = faults.filter(f => f.engineer === engineer.name);
    const completed = engineerFaults.filter(f => f.status === 'تم الإصلاح').length;
    const pending = engineerFaults.filter(f => f.status !== 'تم الإصلاح').length;
    
    return {
      name: engineer.name,
      completed,
      pending,
      total: engineerFaults.length,
      successRate: engineerFaults.length > 0 ? Math.round((completed / engineerFaults.length) * 100) : 0
    };
  }).filter(e => e.total > 0).sort((a, b) => b.completed - a.completed);

  // أنواع الأجهزة الأكثر عطلاً
  const deviceTypeStats = faults.reduce((acc, fault) => {
    acc[fault.deviceType] = (acc[fault.deviceType] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const topDeviceTypes = Object.entries(deviceTypeStats)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 5);

  const handleExportReport = () => {
    // في التطبيق الحقيقي، هذا سيقوم بتصدير التقرير
    alert('سيتم تصدير التقرير قريباً');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">التقارير والإحصائيات</h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            تحليل شامل لأداء النظام والإحصائيات
          </p>
        </div>
        <Button onClick={handleExportReport} className="flex items-center gap-2">
          <Download className="h-4 w-4" />
          تصدير التقرير
        </Button>
      </div>

      {/* الإحصائيات العامة */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="modern-card border-0 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-indigo-500/10" />
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
            <CardTitle className="text-sm font-medium text-foreground/80">إجمالي الأعطال</CardTitle>
            <div className="p-2 rounded-lg bg-blue-500/20">
              <Wrench className="h-4 w-4 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent className="relative z-10">
            <div className="text-2xl font-bold text-blue-600">{totalFaults}</div>
            <p className="text-xs text-muted-foreground">
              {completedFaults} مكتمل، {inProgressFaults + openFaults} قيد المعالجة
            </p>
          </CardContent>
        </Card>

        <Card className="modern-card border-0 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-green-500/10 to-emerald-500/10" />
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
            <CardTitle className="text-sm font-medium text-foreground/80">معدل الإنجاز</CardTitle>
            <div className="p-2 rounded-lg bg-green-500/20">
              <TrendingUp className="h-4 w-4 text-green-600" />
            </div>
          </CardHeader>
          <CardContent className="relative z-10">
            <div className="text-2xl font-bold text-green-600">
              {totalFaults > 0 ? Math.round((completedFaults / totalFaults) * 100) : 0}%
            </div>
            <p className="text-xs text-muted-foreground">
              {completedFaults} من أصل {totalFaults}
            </p>
          </CardContent>
        </Card>

        <Card className="modern-card border-0 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-yellow-500/10 to-orange-500/10" />
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
            <CardTitle className="text-sm font-medium text-foreground/80">أعطال عاجلة</CardTitle>
            <div className="p-2 rounded-lg bg-yellow-500/20">
              <Calendar className="h-4 w-4 text-yellow-600" />
            </div>
          </CardHeader>
          <CardContent className="relative z-10">
            <div className="text-2xl font-bold text-yellow-600">{urgentFaults}</div>
            <p className="text-xs text-muted-foreground">
              تحتاج انتباه فوري
            </p>
          </CardContent>
        </Card>

        <Card className="modern-card border-0 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10" />
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
            <CardTitle className="text-sm font-medium text-foreground/80">مهندسون متاحون</CardTitle>
            <div className="p-2 rounded-lg bg-purple-500/20">
              <Users className="h-4 w-4 text-purple-600" />
            </div>
          </CardHeader>
          <CardContent className="relative z-10">
            <div className="text-2xl font-bold text-purple-600">{availableEngineers}</div>
            <p className="text-xs text-muted-foreground">
              من أصل {totalEngineers} مهندس
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 lg:grid-cols-2">
        {/* إحصائيات حسب الأولوية */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="h-5 w-5" />
              توزيع الأعطال حسب الأولوية
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {priorityStats.map((stat) => (
                <div key={stat.priority} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Badge className={`${stat.color} border`}>
                      {stat.priority}
                    </Badge>
                    <span className="text-sm">{stat.count} عطل</span>
                  </div>
                  <div className="text-sm text-gray-500">
                    {totalFaults > 0 ? Math.round((stat.count / totalFaults) * 100) : 0}%
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* إحصائيات حسب الحالة */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              توزيع الأعطال حسب الحالة
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {statusStats.map((stat) => (
                <div key={stat.status} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Badge className={`${stat.color} border`}>
                      {stat.status}
                    </Badge>
                    <span className="text-sm">{stat.count} عطل</span>
                  </div>
                  <div className="text-sm text-gray-500">
                    {totalFaults > 0 ? Math.round((stat.count / totalFaults) * 100) : 0}%
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* أداء المهندسين */}
      {engineerPerformance.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              أداء المهندسين
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {engineerPerformance.map((engineer) => (
                <div key={engineer.name} className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">{engineer.name}</h4>
                    <Badge variant="outline">
                      {engineer.successRate}% نجاح
                    </Badge>
                  </div>
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div className="text-center">
                      <div className="text-lg font-bold text-blue-600">{engineer.total}</div>
                      <div className="text-xs text-gray-500">إجمالي</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-green-600">{engineer.completed}</div>
                      <div className="text-xs text-gray-500">مكتمل</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-yellow-600">{engineer.pending}</div>
                      <div className="text-xs text-gray-500">معلق</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* أنواع الأجهزة الأكثر عطلاً */}
      {topDeviceTypes.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Wrench className="h-5 w-5" />
              أنواع الأجهزة الأكثر عطلاً
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {topDeviceTypes.map(([deviceType, count], index) => (
                <div key={deviceType} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs">
                      {index + 1}
                    </div>
                    <span className="text-sm">{deviceType}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">{count} عطل</span>
                    <div className="text-xs text-gray-500">
                      {Math.round((count / totalFaults) * 100)}%
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default Reports;
