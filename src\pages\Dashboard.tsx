import { Link } from "react-router-dom";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Wrench, Users, FileText, AlertTriangle } from "lucide-react";
import { useData } from "../context/DataContext";
import { FaultStatusChart } from "@/components/dashboard/FaultStatusChart";
import { FaultStatus } from "@/types";
import { format } from 'date-fns';
import { Badge } from "@/components/ui/badge";

const Dashboard = () => {
  const { faults, engineers } = useData();

  const activeFaults = faults.filter(f => f.status === 'مفتوح' || f.status === 'قيد المعالجة');
  
  const availableEngineers = engineers.filter(e => e.status === 'متاح').length;
  const busyEngineers = engineers.filter(e => e.status === 'مشغول').length;
  const onLeaveEngineers = engineers.filter(e => e.status === 'في إجازة').length;
  const totalEngineers = engineers.length;

  // إحصائيات التخصصات
  const laserEngineers = engineers.filter(e => e.specialty === 'أجهزة ليزر').length;
  const slimmingEngineers = engineers.filter(e => e.specialty === 'أجهزة تخسيس').length;
  const generalEngineers = engineers.filter(e => e.specialty === 'عام').length;
  
  const now = new Date();
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

  const completedThisMonth = faults.filter(f => 
    f.status === 'تم الإصلاح' && f.completedAt && new Date(f.completedAt) >= startOfMonth
  ).length;

  const faultStatusCounts = faults.reduce((acc, fault) => {
    acc[fault.status] = (acc[fault.status] || 0) + 1;
    return acc;
  }, {} as Record<FaultStatus, number>);

  const chartData = Object.entries(faultStatusCounts).map(([name, total]) => ({
    name: name as FaultStatus,
    total,
  }));

  const highPriorityFaults = activeFaults.filter(f => f.priority === 'عاجل');

  const reportsLink = `/reports?from=${format(startOfMonth, 'yyyy-MM-dd')}&to=${format(endOfMonth, 'yyyy-MM-dd')}`;

  return (
    <div className="space-y-8">
      {/* العنوان الرئيسي */}
      <div className="text-center space-y-2">
        <h1 className="text-4xl font-bold bg-gradient-to-r from-primary via-accent to-primary bg-clip-text text-transparent">
          لوحة التحكم
        </h1>
        <p className="text-muted-foreground">نظرة شاملة على حالة النظام</p>
      </div>

      {/* الكروت الإحصائية */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Link to="/faults?status=مفتوح&status=قيد المعالجة" className="group">
          <Card className="modern-card hover:scale-105 transition-all duration-300 border-0 relative overflow-hidden">
            {/* خلفية متدرجة */}
            <div className="absolute inset-0 bg-gradient-to-br from-orange-500/10 to-red-500/10" />

            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
              <CardTitle className="text-sm font-medium text-foreground/80">
                الأعطال النشطة
              </CardTitle>
              <div className="p-2 rounded-lg bg-orange-500/20">
                <Wrench className="h-5 w-5 text-orange-600" />
              </div>
            </CardHeader>
            <CardContent className="relative z-10">
              <div className="text-3xl font-bold text-orange-600">{activeFaults.length}</div>
              <p className="text-xs text-muted-foreground mt-1">
                أعطال مفتوحة أو قيد المعالجة
              </p>
              <div className="mt-3 h-1 bg-orange-200 rounded-full overflow-hidden">
                <div className="h-full bg-gradient-to-r from-orange-400 to-orange-600 rounded-full w-3/4" />
              </div>
            </CardContent>
          </Card>
        </Link>
        <Link to="/engineers?status=متاح" className="group">
          <Card className="modern-card hover:scale-105 transition-all duration-300 border-0 relative overflow-hidden">
            {/* خلفية متدرجة */}
            <div className="absolute inset-0 bg-gradient-to-br from-green-500/10 to-emerald-500/10" />

            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
              <CardTitle className="text-sm font-medium text-foreground/80">
                مهندسون متاحون
              </CardTitle>
              <div className="p-2 rounded-lg bg-green-500/20">
                <Users className="h-5 w-5 text-green-600" />
              </div>
            </CardHeader>
            <CardContent className="relative z-10">
              <div className="text-3xl font-bold text-green-600">{availableEngineers}</div>
              <p className="text-xs text-muted-foreground mt-1">
                من أصل {totalEngineers} مهندس
              </p>
              <div className="mt-3 h-1 bg-green-200 rounded-full overflow-hidden">
                <div
                  className="h-full bg-gradient-to-r from-green-400 to-green-600 rounded-full transition-all duration-500"
                  style={{ width: `${(availableEngineers / totalEngineers) * 100}%` }}
                />
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link to="/engineers?status=مشغول" className="group">
          <Card className="modern-card hover:scale-105 transition-all duration-300 border-0 relative overflow-hidden">
            {/* خلفية متدرجة */}
            <div className="absolute inset-0 bg-gradient-to-br from-yellow-500/10 to-orange-500/10" />

            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
              <CardTitle className="text-sm font-medium text-foreground/80">
                مهندسون مشغولون
              </CardTitle>
              <div className="p-2 rounded-lg bg-yellow-500/20">
                <Users className="h-5 w-5 text-yellow-600" />
              </div>
            </CardHeader>
            <CardContent className="relative z-10">
              <div className="text-3xl font-bold text-yellow-600">{busyEngineers}</div>
              <p className="text-xs text-muted-foreground mt-1">
                يعملون حالياً على أعطال
              </p>
              <div className="mt-3 h-1 bg-yellow-200 rounded-full overflow-hidden">
                <div
                  className="h-full bg-gradient-to-r from-yellow-400 to-orange-600 rounded-full transition-all duration-500"
                  style={{ width: `${totalEngineers > 0 ? (busyEngineers / totalEngineers) * 100 : 0}%` }}
                />
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link to={reportsLink} className="group">
          <Card className="modern-card hover:scale-105 transition-all duration-300 border-0 relative overflow-hidden">
            {/* خلفية متدرجة */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10" />

            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
              <CardTitle className="text-sm font-medium text-foreground/80">
                التقارير المنجزة (هذا الشهر)
              </CardTitle>
              <div className="p-2 rounded-lg bg-blue-500/20">
                <FileText className="h-5 w-5 text-blue-600" />
              </div>
            </CardHeader>
            <CardContent className="relative z-10">
              <div className="text-3xl font-bold text-blue-600">{completedThisMonth}</div>
              <p className="text-xs text-muted-foreground mt-1">
                منذ بداية الشهر
              </p>
              <div className="mt-3 h-1 bg-blue-200 rounded-full overflow-hidden">
                <div className="h-full bg-gradient-to-r from-blue-400 to-purple-600 rounded-full w-2/3" />
              </div>
            </CardContent>
          </Card>
        </Link>
      </div>

      {/* إحصائيات تخصصات المهندسين */}
      <div className="space-y-4">
        <h2 className="text-2xl font-bold">توزيع المهندسين حسب التخصص</h2>
        <div className="grid gap-4 md:grid-cols-3">
          <Card className="modern-card border-0 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10" />
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
              <CardTitle className="text-sm font-medium text-foreground/80">أجهزة ليزر</CardTitle>
              <div className="p-2 rounded-lg bg-purple-500/20">
                <Users className="h-4 w-4 text-purple-600" />
              </div>
            </CardHeader>
            <CardContent className="relative z-10">
              <div className="text-2xl font-bold text-purple-600">{laserEngineers}</div>
              <p className="text-xs text-muted-foreground">مهندس متخصص</p>
            </CardContent>
          </Card>

          <Card className="modern-card border-0 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-teal-500/10 to-cyan-500/10" />
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
              <CardTitle className="text-sm font-medium text-foreground/80">أجهزة تخسيس</CardTitle>
              <div className="p-2 rounded-lg bg-teal-500/20">
                <Users className="h-4 w-4 text-teal-600" />
              </div>
            </CardHeader>
            <CardContent className="relative z-10">
              <div className="text-2xl font-bold text-teal-600">{slimmingEngineers}</div>
              <p className="text-xs text-muted-foreground">مهندس متخصص</p>
            </CardContent>
          </Card>

          <Card className="modern-card border-0 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/10 to-blue-500/10" />
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
              <CardTitle className="text-sm font-medium text-foreground/80">عام</CardTitle>
              <div className="p-2 rounded-lg bg-indigo-500/20">
                <Users className="h-4 w-4 text-indigo-600" />
              </div>
            </CardHeader>
            <CardContent className="relative z-10">
              <div className="text-2xl font-bold text-indigo-600">{generalEngineers}</div>
              <p className="text-xs text-muted-foreground">مهندس عام</p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* الرسوم البيانية والأعطال العاجلة */}
      <div className="grid gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2">
          <Card className="modern-card border-0 h-full">
            <div className="absolute inset-0 bg-gradient-to-br from-card to-card/80" />
            <div className="relative z-10">
              <FaultStatusChart data={chartData} />
            </div>
          </Card>
        </div>

        <Card className="modern-card border-0 relative overflow-hidden">
          {/* خلفية متدرجة للأعطال العاجلة */}
          <div className="absolute inset-0 bg-gradient-to-br from-red-500/5 to-orange-500/5" />

          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
            <CardTitle className="text-sm font-medium">أعطال عاجلة نشطة</CardTitle>
            <div className="p-2 rounded-lg bg-red-500/20">
              <AlertTriangle className="h-5 w-5 text-red-600" />
            </div>
          </CardHeader>

          <CardContent className="relative z-10">
            {highPriorityFaults.length > 0 ? (
              <div className="space-y-3">
                {highPriorityFaults.map(fault => (
                  <div key={fault.id} className="group p-3 rounded-lg bg-gradient-to-r from-red-50 to-orange-50 hover:from-red-100 hover:to-orange-100 transition-all duration-200 border border-red-200/50">
                    <div className="flex items-center justify-between">
                      <Link
                        to={`/faults/${fault.id}`}
                        className="text-sm font-medium text-red-700 hover:text-red-800 group-hover:underline transition-colors"
                      >
                        {fault.customerName}
                      </Link>
                      <Badge variant="destructive" className="bg-red-500 hover:bg-red-600">
                        {fault.priority}
                      </Badge>
                    </div>
                    <p className="text-xs text-red-600/70 mt-1">#{fault.id}</p>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-green-100 flex items-center justify-center">
                  <AlertTriangle className="h-8 w-8 text-green-600" />
                </div>
                <p className="text-sm text-muted-foreground">لا توجد أعطال عاجلة حاليًا</p>
                <p className="text-xs text-green-600 mt-1">الوضع مستقر ✨</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;