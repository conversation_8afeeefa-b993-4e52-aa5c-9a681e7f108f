import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { PlusCircle } from "lucide-react";
import { EngineerSpecialty } from "@/types";
import { showSuccess } from "@/utils/toast";
import { useData } from "../../context/DataContext";

const specialties: [EngineerSpecialty, ...EngineerSpecialty[]] = ["أجهزة ليزر", "أجهزة تخسيس", "عام"];

const formSchema = z.object({
  name: z.string().min(2, { message: "الاسم مطلوب." }),
  phone: z.string().min(10, { message: "رقم هاتف صالح مطلوب." }),
  email: z.string().email({ message: "بريد إلكتروني غير صالح." }),
  area: z.string().min(2, { message: "المنطقة مطلوبة." }),
  specialty: z.enum(specialties, { required_error: "الرجاء اختيار التخصص." }),
});

const defaultFormValues: z.infer<typeof formSchema> = {
  name: "",
  phone: "",
  email: "",
  area: "",
  specialty: "عام",
};

export function CreateEngineerDialog() {
  const [open, setOpen] = useState(false);
  const { addEngineer } = useData();
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: defaultFormValues,
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    addEngineer(values);
    showSuccess("تم إضافة المهندس بنجاح!");
    form.reset(defaultFormValues);
    setOpen(false);
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
          <PlusCircle className="me-2 h-4 w-4" /> إضافة مهندس جديد
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[525px]">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <DialogHeader>
              <DialogTitle>إضافة مهندس جديد</DialogTitle>
              <DialogDescription>
                يرجى ملء بيانات المهندس. سيتم تعيين حالته "متاح" بشكل افتراضي.
              </DialogDescription>
            </DialogHeader>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-4">
              <FormField control={form.control} name="name" render={({ field }) => (
                <FormItem><FormLabel>اسم المهندس</FormLabel><FormControl><Input placeholder="مثال: علي محمد" {...field} /></FormControl><FormMessage /></FormItem>
              )} />
              <FormField control={form.control} name="phone" render={({ field }) => (
                <FormItem><FormLabel>رقم الهاتف</FormLabel><FormControl><Input placeholder="01xxxxxxxxx" {...field} /></FormControl><FormMessage /></FormItem>
              )} />
              <FormField control={form.control} name="email" render={({ field }) => (
                <FormItem className="md:col-span-2"><FormLabel>البريد الإلكتروني</FormLabel><FormControl><Input placeholder="<EMAIL>" {...field} /></FormControl><FormMessage /></FormItem>
              )} />
              <FormField control={form.control} name="specialty" render={({ field }) => (
                <FormItem><FormLabel>التخصص</FormLabel><Select onValueChange={field.onChange} defaultValue={field.value}><FormControl><SelectTrigger><SelectValue placeholder="اختر التخصص" /></SelectTrigger></FormControl><SelectContent>{specialties.map(type => <SelectItem key={type} value={type}>{type}</SelectItem>)}</SelectContent></Select><FormMessage /></FormItem>
              )} />
               <FormField control={form.control} name="area" render={({ field }) => (
                <FormItem><FormLabel>المنطقة الجغرافية</FormLabel><FormControl><Input placeholder="مثال: القاهرة الكبرى" {...field} /></FormControl><FormMessage /></FormItem>
              )} />
            </div>
            <DialogFooter className="pt-4">
              <DialogClose asChild><Button type="button" variant="secondary">إلغاء</Button></DialogClose>
              <Button type="submit">حفظ</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}