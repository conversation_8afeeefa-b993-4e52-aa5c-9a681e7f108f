import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { VideoUpload } from "./VideoUpload";
import { VideoViewer } from "./VideoViewer";
import { Fault, VideoAttachment } from "@/types";
import { useData } from "@/context/DataContext";
import { Video, Upload, Eye } from "lucide-react";

interface VideoManagementProps {
  fault: Fault;
  canEdit?: boolean;
}

export function VideoManagement({ fault, canEdit = true }: VideoManagementProps) {
  const { updateFaultVideos } = useData();
  const [activeTab, setActiveTab] = useState<string>("view");

  const handleVideosBeforeChange = (videos: VideoAttachment[]) => {
    updateFaultVideos(fault.id, videos, fault.videosAfterMaintenance);
  };

  const handleVideosAfterChange = (videos: VideoAttachment[]) => {
    updateFaultVideos(fault.id, fault.videosBeforeMaintenance, videos);
  };

  const hasVideos = (fault.videosBeforeMaintenance?.length || 0) + (fault.videosAfterMaintenance?.length || 0) > 0;

  // إذا لم تكن هناك فيديوهات ولا يمكن التعديل، لا نعرض شيئاً
  if (!hasVideos && !canEdit) {
    return null;
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Video className="h-5 w-5" />
          فيديوهات الصيانة
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          توثيق حالة الجهاز قبل وبعد الصيانة
        </p>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="view" className="flex items-center gap-2">
              <Eye className="h-4 w-4" />
              عرض الفيديوهات
              {hasVideos && (
                <span className="bg-primary text-primary-foreground text-xs px-2 py-1 rounded-full">
                  {(fault.videosBeforeMaintenance?.length || 0) + (fault.videosAfterMaintenance?.length || 0)}
                </span>
              )}
            </TabsTrigger>
            {canEdit && (
              <TabsTrigger value="upload" className="flex items-center gap-2">
                <Upload className="h-4 w-4" />
                رفع فيديوهات
              </TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="view" className="mt-6">
            <VideoViewer
              videosBeforeMaintenance={fault.videosBeforeMaintenance}
              videosAfterMaintenance={fault.videosAfterMaintenance}
              title="فيديوهات الصيانة"
            />
          </TabsContent>

          {canEdit && (
            <TabsContent value="upload" className="mt-6 space-y-6">
              <VideoUpload
                videos={fault.videosBeforeMaintenance || []}
                onVideosChange={handleVideosBeforeChange}
                type="before"
                title="فيديوهات قبل الصيانة"
                description="قم برفع فيديوهات توضح حالة الجهاز قبل بدء عملية الصيانة"
                disabled={fault.status === 'تم الإصلاح'}
              />

              <VideoUpload
                videos={fault.videosAfterMaintenance || []}
                onVideosChange={handleVideosAfterChange}
                type="after"
                title="فيديوهات بعد الصيانة"
                description="قم برفع فيديوهات توضح حالة الجهاز بعد إتمام عملية الصيانة"
                disabled={fault.status !== 'تم الإصلاح' && fault.status !== 'قيد المعالجة'}
              />

              {fault.status === 'تم الإصلاح' && (
                <div className="bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                  <div className="flex items-center gap-2 text-green-700 dark:text-green-300">
                    <Video className="h-5 w-5" />
                    <span className="font-medium">تم إكمال الصيانة</span>
                  </div>
                  <p className="text-sm text-green-600 dark:text-green-400 mt-1">
                    يمكنك الآن رفع فيديوهات بعد الصيانة لتوثيق حالة الجهاز النهائية
                  </p>
                </div>
              )}

              {fault.status === 'قيد المعالجة' && (
                <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                  <div className="flex items-center gap-2 text-blue-700 dark:text-blue-300">
                    <Video className="h-5 w-5" />
                    <span className="font-medium">الصيانة قيد التنفيذ</span>
                  </div>
                  <p className="text-sm text-blue-600 dark:text-blue-400 mt-1">
                    يمكنك رفع فيديوهات قبل الصيانة. فيديوهات ما بعد الصيانة ستكون متاحة عند إكمال الإصلاح
                  </p>
                </div>
              )}

              {fault.status === 'مفتوح' && (
                <div className="bg-orange-50 dark:bg-orange-950/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4">
                  <div className="flex items-center gap-2 text-orange-700 dark:text-orange-300">
                    <Video className="h-5 w-5" />
                    <span className="font-medium">في انتظار بدء الصيانة</span>
                  </div>
                  <p className="text-sm text-orange-600 dark:text-orange-400 mt-1">
                    يمكنك رفع فيديوهات قبل الصيانة لتوثيق الحالة الأولية للجهاز
                  </p>
                </div>
              )}
            </TabsContent>
          )}
        </Tabs>
      </CardContent>
    </Card>
  );
}
