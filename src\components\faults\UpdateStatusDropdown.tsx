import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Fault, FaultStatus } from "@/types";
import { ChevronDown } from "lucide-react";
import { possibleStatusTransitions } from "@/lib/fault-utils";

interface UpdateStatusDropdownProps {
  fault: Fault;
  onStatusChange: (newStatus: FaultStatus) => void;
}

export function UpdateStatusDropdown({ fault, onStatusChange }: UpdateStatusDropdownProps) {
  const availableStatuses = possibleStatusTransitions[fault.status] || [];

  if (availableStatuses.length === 0) {
    return null;
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline">
          تغيير الحالة
          <ChevronDown className="ms-2 h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>تحديث حالة العطل</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {availableStatuses.map(status => (
          <DropdownMenuItem key={status} onClick={() => onStatusChange(status)}>
            تغيير إلى "{status}"
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}