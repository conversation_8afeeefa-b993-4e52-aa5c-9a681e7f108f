import { useData } from "../context/DataContext";
import { CreateFaultDialog } from "@/components/faults/CreateFaultDialog";
import { FaultsDataTable } from "@/components/faults/FaultsDataTable";
import { columns } from "@/components/faults/columns";
import { useSearchParams } from "react-router-dom";
import { ColumnFiltersState } from "@tanstack/react-table";

const Faults = () => {
  const { faults } = useData();
  const [searchParams] = useSearchParams();

  const statusParams = searchParams.getAll("status");
  const initialColumnFilters: ColumnFiltersState = [];
  if (statusParams.length > 0) {
    initialColumnFilters.push({
      id: 'status',
      value: statusParams,
    });
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">إدارة الأعطال</h1>
        <CreateFaultDialog />
      </div>
      <FaultsDataTable columns={columns} data={faults} initialColumnFilters={initialColumnFilters} />
    </div>
  );
};

export default Faults;