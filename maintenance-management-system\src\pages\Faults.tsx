import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus, Search, Filter, Eye, Wrench, Clock, CheckCircle, XCircle } from "lucide-react";
import { useData } from "@/context/DataContext";
import { Fault, FaultStatus, FaultPriority } from "@/types";
import CreateFaultDialog from "@/components/faults/CreateFaultDialog";

const Faults = () => {
  const { faults } = useData();
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<FaultStatus | "all">("all");
  const [priorityFilter, setPriorityFilter] = useState<FaultPriority | "all">("all");
  const [createDialogOpen, setCreateDialogOpen] = useState(false);

  // تصفية الأعطال
  const filteredFaults = faults.filter((fault) => {
    const matchesSearch = 
      fault.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      fault.deviceType.toLowerCase().includes(searchTerm.toLowerCase()) ||
      fault.id.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === "all" || fault.status === statusFilter;
    const matchesPriority = priorityFilter === "all" || fault.priority === priorityFilter;
    
    return matchesSearch && matchesStatus && matchesPriority;
  });

  const getStatusIcon = (status: FaultStatus) => {
    switch (status) {
      case "مفتوح":
        return <Clock className="h-4 w-4" />;
      case "قيد المعالجة":
        return <Wrench className="h-4 w-4" />;
      case "تم الإصلاح":
        return <CheckCircle className="h-4 w-4" />;
      case "لم يتم الإصلاح":
        return <XCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: FaultStatus) => {
    switch (status) {
      case "مفتوح":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "قيد المعالجة":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "تم الإصلاح":
        return "bg-green-100 text-green-800 border-green-200";
      case "لم يتم الإصلاح":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getPriorityColor = (priority: FaultPriority) => {
    switch (priority) {
      case "عاجل":
        return "bg-red-100 text-red-800 border-red-200";
      case "متوسط":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "عادي":
        return "bg-green-100 text-green-800 border-green-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">الأعطال</h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            إدارة ومتابعة جميع أعطال الأجهزة
          </p>
        </div>
        <Button onClick={() => setCreateDialogOpen(true)} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          إضافة عطل جديد
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            البحث والتصفية
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="البحث بالاسم أو نوع الجهاز أو رقم العطل..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10"
              />
            </div>
            
            <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as FaultStatus | "all")}>
              <SelectTrigger>
                <SelectValue placeholder="تصفية حسب الحالة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الحالات</SelectItem>
                <SelectItem value="مفتوح">مفتوح</SelectItem>
                <SelectItem value="قيد المعالجة">قيد المعالجة</SelectItem>
                <SelectItem value="تم الإصلاح">تم الإصلاح</SelectItem>
                <SelectItem value="لم يتم الإصلاح">لم يتم الإصلاح</SelectItem>
              </SelectContent>
            </Select>

            <Select value={priorityFilter} onValueChange={(value) => setPriorityFilter(value as FaultPriority | "all")}>
              <SelectTrigger>
                <SelectValue placeholder="تصفية حسب الأولوية" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الأولويات</SelectItem>
                <SelectItem value="عاجل">عاجل</SelectItem>
                <SelectItem value="متوسط">متوسط</SelectItem>
                <SelectItem value="عادي">عادي</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            عرض {filteredFaults.length} من أصل {faults.length} عطل
          </p>
        </div>

        {filteredFaults.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <Wrench className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                لا توجد أعطال
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                لم يتم العثور على أعطال تطابق معايير البحث
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-4">
            {filteredFaults.map((fault) => (
              <Card key={fault.id} className="hover:shadow-lg transition-shadow duration-200">
                <CardContent className="p-6">
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                    <div className="flex-1 space-y-3">
                      <div className="flex items-start justify-between">
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                            {fault.customerName}
                          </h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {fault.deviceType} • {fault.id}
                          </p>
                        </div>
                        <div className="flex gap-2">
                          <Badge className={`${getPriorityColor(fault.priority)} border`}>
                            {fault.priority}
                          </Badge>
                          <Badge className={`${getStatusColor(fault.status)} border flex items-center gap-1`}>
                            {getStatusIcon(fault.status)}
                            {fault.status}
                          </Badge>
                        </div>
                      </div>

                      <p className="text-sm text-gray-700 dark:text-gray-300 line-clamp-2">
                        {fault.faultDescription}
                      </p>

                      <div className="flex flex-wrap gap-4 text-xs text-gray-500 dark:text-gray-400">
                        <span>📅 {new Date(fault.reportedAt).toLocaleDateString('ar-EG')}</span>
                        <span>📍 {fault.facilityAddress}</span>
                        {fault.engineer && <span>👨‍🔧 {fault.engineer}</span>}
                        {fault.videosBeforeMaintenance && fault.videosBeforeMaintenance.length > 0 && (
                          <span>🎥 {fault.videosBeforeMaintenance.length} فيديو قبل</span>
                        )}
                        {fault.videosAfterMaintenance && fault.videosAfterMaintenance.length > 0 && (
                          <span>🎬 {fault.videosAfterMaintenance.length} فيديو بعد</span>
                        )}
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Link to={`/faults/${fault.id}`}>
                        <Button variant="outline" size="sm" className="flex items-center gap-2">
                          <Eye className="h-4 w-4" />
                          عرض التفاصيل
                        </Button>
                      </Link>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Create Fault Dialog */}
      <CreateFaultDialog 
        open={createDialogOpen} 
        onOpenChange={setCreateDialogOpen}
      />
    </div>
  );
};

export default Faults;
