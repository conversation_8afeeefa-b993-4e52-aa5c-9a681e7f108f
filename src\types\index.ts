export type FaultPriority = "عاجل" | "متوسط" | "عادي";
export type FaultStatus = "مفتوح" | "قيد المعالجة" | "تم الإصلاح" | "لم يتم الإصلاح" | "معلق";
// تم تغيير DeviceType إلى string للسماح بالإدخال اليدوي
export type DeviceType = string;

export interface Attachment {
  name: string;
  type: string;
  size: number;
}

export interface Fault {
  id: string;
  customerName: string;
  customerPhone: string;
  customerEmail: string;
  facilityAddress: string;
  deviceType: DeviceType;
  serialNumber?: string;
  description: string;
  priority: FaultPriority;
  status: FaultStatus;
  reportedAt: string; // Using string for simplicity, can be Date object
  completedAt?: string; // Date when the fault was marked as "تم الإصلاح"
  attachments?: Attachment[];
  engineer?: string; // Later will be Engineer ID
}

export type EngineerSpecialty = "أجهزة ليزر" | "أجهزة تخسيس" | "عام";
export type EngineerStatus = "متاح" | "مشغول" | "في إجازة";

export interface Engineer {
  id: string;
  name: string;
  phone: string;
  email: string;
  specialty: EngineerSpecialty;
  status: EngineerStatus;
  area: string; // المنطقة الجغرافية
}