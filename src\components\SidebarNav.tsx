import { NavLink } from "react-router-dom";
import { LayoutDashboard, Wrench, Users, FileText, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";

const SidebarLink = ({ to, icon, children, onClick }: { to: string; icon: React.ReactNode; children: React.ReactNode; onClick?: () => void; }) => {
  return (
    <NavLink
      to={to}
      end
      onClick={onClick}
      className={({ isActive }) =>
        cn(
          "group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 relative overflow-hidden modern-button",
          isActive
            ? "bg-gradient-to-r from-primary to-primary/80 text-primary-foreground shadow-lg shadow-primary/25"
            : "text-foreground/70 hover:bg-gradient-to-r hover:from-muted hover:to-muted/80 hover:text-foreground hover:shadow-md"
        )
      }
    >
      <div className="flex items-center flex-1 relative z-10">
        <div className={cn(
          "p-2 rounded-lg transition-all duration-300",
          "group-hover:scale-110"
        )}>
          {icon}
        </div>
        <span className="ms-3 font-medium">{children}</span>
      </div>
      <ChevronRight className={cn(
        "h-4 w-4 transition-all duration-300 opacity-0 group-hover:opacity-100",
        "group-hover:translate-x-1"
      )} />
    </NavLink>
  );
};

export const SidebarNav = ({ onLinkClick }: { onLinkClick?: () => void }) => {
    return (
        <nav className="flex-1 p-4 space-y-3">
            <div className="mb-6">
                <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-3 px-4">
                    القائمة الرئيسية
                </h3>
                <div className="space-y-2">
                    <SidebarLink
                        to="/"
                        icon={<LayoutDashboard size={20} className="text-blue-500" />}
                        onClick={onLinkClick}
                    >
                        لوحة التحكم
                    </SidebarLink>
                    <SidebarLink
                        to="/faults"
                        icon={<Wrench size={20} className="text-orange-500" />}
                        onClick={onLinkClick}
                    >
                        الأعطال
                    </SidebarLink>
                    <SidebarLink
                        to="/engineers"
                        icon={<Users size={20} className="text-green-500" />}
                        onClick={onLinkClick}
                    >
                        المهندسون
                    </SidebarLink>
                    <SidebarLink
                        to="/reports"
                        icon={<FileText size={20} className="text-purple-500" />}
                        onClick={onLinkClick}
                    >
                        التقارير
                    </SidebarLink>
                </div>
            </div>
        </nav>
    );
}