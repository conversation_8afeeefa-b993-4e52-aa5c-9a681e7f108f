import { useState } from "react";
import { DateRange } from "react-day-picker";
import { useSearchParams } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { FaultsDataTable } from "@/components/faults/FaultsDataTable";
import { columns } from "@/components/faults/columns";
import { useData } from "../context/DataContext";
import { DeviceType } from "@/types";
import { CheckSquare, Timer, Wrench, FileSpreadsheet, BarChart3 } from "lucide-react";
import { DateRangePicker } from "@/components/shared/DateRangePicker";
import { ExportToPDF } from "@/components/reports/ExportToPDF";
import { ExcelUploader } from "@/components/reports/ExcelUploader";
import { differenceInHours } from 'date-fns';

const Reports = () => {
  const { faults } = useData();
  const [searchParams] = useSearchParams();

  const fromParam = searchParams.get('from');
  const toParam = searchParams.get('to');

  const [date, setDate] = useState<DateRange | undefined>(() => {
    if (fromParam) {
        try {
            const from = new Date(fromParam);
            if (isNaN(from.getTime())) return undefined;

            const to = toParam ? new Date(toParam) : undefined;
            if (to && isNaN(to.getTime())) return { from };

            return { from, to };
        } catch (e) {
            return undefined;
        }
    }
    return undefined;
  });

  const completedReports = faults.filter(fault => fault.status === "تم الإصلاح" && fault.completedAt);

  const filteredReports = completedReports.filter(fault => {
    if (!date?.from || !fault.completedAt) {
      return true;
    }
    const completedDate = new Date(fault.completedAt);
    const from = new Date(date.from);
    from.setHours(0, 0, 0, 0);

    if (date.to) {
      const to = new Date(date.to);
      to.setHours(23, 59, 59, 999);
      return completedDate >= from && completedDate <= to;
    }
    return completedDate >= from;
  });

  const totalCompleted = filteredReports.length;

  const deviceTypeCounts = filteredReports.reduce((acc, fault) => {
      acc[fault.deviceType] = (acc[fault.deviceType] || 0) + 1;
      return acc;
  }, {} as Record<DeviceType, number>);

  const mostFrequentDevice = Object.entries(deviceTypeCounts).sort((a, b) => b[1] - a[1])[0]?.[0] || "لا يوجد";

  const calculateAverageTime = () => {
    if (filteredReports.length === 0) return "N/A";
    const totalHours = filteredReports.reduce((acc, fault) => {
      const reportedAt = new Date(fault.reportedAt);
      const completedAt = new Date(fault.completedAt!);
      return acc + differenceInHours(completedAt, reportedAt);
    }, 0);
    const averageHours = totalHours / filteredReports.length;
    return `${averageHours.toFixed(1)} ساعة`;
  }

  return (
    <div className="space-y-8">
      {/* العنوان الرئيسي */}
      <div className="text-center space-y-2">
        <h1 className="text-4xl font-bold bg-gradient-to-r from-primary via-accent to-primary bg-clip-text text-transparent">
          مركز التقارير والتحليلات
        </h1>
        <p className="text-muted-foreground">تقارير شاملة وتحليل البيانات</p>
      </div>

      {/* التبويبات */}
      <Tabs defaultValue="system-reports" className="w-full">
        <TabsList className="grid w-full grid-cols-2 lg:w-auto lg:grid-cols-2">
          <TabsTrigger value="system-reports" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            تقارير النظام
          </TabsTrigger>
          <TabsTrigger value="excel-reports" className="flex items-center gap-2">
            <FileSpreadsheet className="h-4 w-4" />
            تقارير CSV
          </TabsTrigger>
        </TabsList>

        {/* تقارير النظام */}
        <TabsContent value="system-reports" className="space-y-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <h2 className="text-2xl font-semibold">أرشيف التقارير المنجزة</h2>
            <div className="flex flex-col sm:flex-row gap-2">
              <DateRangePicker date={date} setDate={setDate} />
              <ExportToPDF
                data={filteredReports}
                dateRange={date}
                totalCompleted={totalCompleted}
                averageTime={calculateAverageTime()}
                mostFrequentDevice={mostFrequentDevice}
              />
            </div>
          </div>
      
          {/* الكروت الإحصائية */}
          <div className="grid gap-6 md:grid-cols-3">
            <Card className="modern-card border-0 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-green-500/10 to-emerald-500/10" />
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
                <CardTitle className="text-sm font-medium">
                  إجمالي التقارير المنجزة
                </CardTitle>
                <div className="p-2 rounded-lg bg-green-500/20">
                  <CheckSquare className="h-5 w-5 text-green-600" />
                </div>
              </CardHeader>
              <CardContent className="relative z-10">
                <div className="text-3xl font-bold text-green-600">{totalCompleted}</div>
                <p className="text-xs text-muted-foreground mt-1">
                  في الفترة المحددة
                </p>
              </CardContent>
            </Card>

            <Card className="modern-card border-0 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-cyan-500/10" />
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
                <CardTitle className="text-sm font-medium">
                  متوسط وقت الإصلاح
                </CardTitle>
                <div className="p-2 rounded-lg bg-blue-500/20">
                  <Timer className="h-5 w-5 text-blue-600" />
                </div>
              </CardHeader>
              <CardContent className="relative z-10">
                <div className="text-3xl font-bold text-blue-600">{calculateAverageTime()}</div>
                <p className="text-xs text-muted-foreground mt-1">
                  للأعطال المكتملة
                </p>
              </CardContent>
            </Card>

            <Card className="modern-card border-0 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10" />
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
                <CardTitle className="text-sm font-medium">
                  الجهاز الأكثر أعطالاً
                </CardTitle>
                <div className="p-2 rounded-lg bg-purple-500/20">
                  <Wrench className="h-5 w-5 text-purple-600" />
                </div>
              </CardHeader>
              <CardContent className="relative z-10">
                <div className="text-lg font-bold text-purple-600">{mostFrequentDevice}</div>
                <p className="text-xs text-muted-foreground mt-1">
                  في الفترة المحددة
                </p>
              </CardContent>
            </Card>
          </div>

          {/* جدول التقارير */}
          <Card className="modern-card border-0">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-primary" />
                تفاصيل التقارير المنجزة
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="table-modern rounded-lg overflow-hidden">
                <FaultsDataTable columns={columns} data={filteredReports} />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* تقارير CSV */}
        <TabsContent value="excel-reports" className="space-y-6">
          <div className="text-center space-y-2 mb-6">
            <h2 className="text-2xl font-semibold">تقارير من ملفات CSV</h2>
            <p className="text-muted-foreground">ارفع ملف CSV لإنشاء تقرير مفصل عليه</p>
          </div>

          <ExcelUploader />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Reports;