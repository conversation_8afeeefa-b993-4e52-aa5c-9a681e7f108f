"use client"

import { useState } from "react"
import { ColumnDef } from "@tanstack/react-table"
import { <PERSON> } from "react-router-dom"
import { Engineer, EngineerStatus, EngineerSpecialty } from "@/types"
import { Badge } from "@/components/ui/badge"
import { MoreHorizontal, Trash2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { EditEngineerDialog } from "./EditEngineerDialog"
import { ConfirmationDialog } from "../shared/ConfirmationDialog"
import { useData } from "@/context/DataContext"
import { showSuccess } from "@/utils/toast"
import { DataTableColumnHeader } from "../shared/DataTableColumnHeader"

const statusVariant: Record<EngineerStatus, "default" | "secondary" | "outline"> = {
  "متاح": "default",
  "مشغول": "secondary",
  "في إجازة": "outline",
}

const ActionsCell = ({ engineer }: { engineer: Engineer }) => {
  const [isEditDialogOpen, setEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const { deleteEngineer } = useData();

  const handleDelete = () => {
    deleteEngineer(engineer.id);
    showSuccess("تم حذف المهندس بنجاح.");
    setDeleteDialogOpen(false);
  };

  return (
    <>
      <EditEngineerDialog 
        engineer={engineer} 
        open={isEditDialogOpen} 
        onOpenChange={setEditDialogOpen} 
      />
      <ConfirmationDialog
        open={isDeleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleDelete}
        title={`هل أنت متأكد من حذف ${engineer.name}؟`}
        description="سيتم حذف هذا المهندس بشكل دائم وإلغاء إسناد أي أعطال مرتبطة به. لا يمكن التراجع عن هذا الإجراء."
      />
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">فتح القائمة</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>إجراءات</DropdownMenuLabel>
          <DropdownMenuItem onClick={() => navigator.clipboard.writeText(engineer.id)}>
            نسخ الرقم التعريفي
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => setEditDialogOpen(true)}>
            تعديل البيانات
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => setDeleteDialogOpen(true)} className="text-red-600 focus:text-red-600">
            <Trash2 className="me-2 h-4 w-4" />
            حذف
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  )
}

export const columns: ColumnDef<Engineer>[] = [
  {
    accessorKey: "name",
    header: ({ column }) => <DataTableColumnHeader column={column} title="الاسم" />,
    cell: ({ row }) => {
      const engineer = row.original
      return (
        <Link to={`/engineers/${engineer.id}`} className="font-medium text-primary hover:underline">
          {engineer.name}
        </Link>
      )
    },
  },
  {
    accessorKey: "phone",
    header: ({ column }) => <DataTableColumnHeader column={column} title="رقم الهاتف" />,
  },
  {
    accessorKey: "specialty",
    header: ({ column }) => <DataTableColumnHeader column={column} title="التخصص" />,
    cell: ({ row }) => {
      const specialty = row.getValue("specialty") as EngineerSpecialty
      return <Badge variant="secondary">{specialty}</Badge>
    },
  },
  {
    accessorKey: "status",
    header: ({ column }) => <DataTableColumnHeader column={column} title="الحالة" />,
    cell: ({ row }) => {
        const status = row.getValue("status") as EngineerStatus
        return <Badge variant={statusVariant[status] || "default"}>{status}</Badge>
    }
  },
  {
    accessorKey: "area",
    header: ({ column }) => <DataTableColumnHeader column={column} title="المنطقة" />,
  },
  {
    id: "actions",
    cell: ({ row }) => <ActionsCell engineer={row.original} />,
  },
]