import { useState } from "react";
import { useParams } from "react-router-dom";
import { useData } from "../context/DataContext";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { User, Phone, Mail, MapPin, Wrench, Hash, Calendar, ShieldAlert, CheckCircle, UserPlus, Pencil, Paperclip } from "lucide-react";
import { AssignEngineerDialog } from "@/components/faults/AssignEngineerDialog";
import { UpdateStatusDropdown } from "@/components/faults/UpdateStatusDropdown";
import { EditFaultDialog } from "@/components/faults/EditFaultDialog";
import { PrintFaultReport } from "@/components/faults/PrintFaultReport";
import { showSuccess } from "@/utils/toast";
import { FaultStatus } from "@/types";

const FaultDetails = () => {
  const { faultId } = useParams<{ faultId: string }>();
  const { faults, getAvailableEngineers, assignEngineerToFault, updateFaultStatus } = useData();
  
  const fault = faults.find((f) => f.id === faultId);
  const [isAssignDialogOpen, setAssignDialogOpen] = useState(false);
  const [isEditDialogOpen, setEditDialogOpen] = useState(false);

  if (!fault) {
    return (
      <div className="text-center">
        <h1 className="text-2xl font-bold">العطل غير موجود</h1>
        <p className="text-muted-foreground">لم نتمكن من العثور على تفاصيل العطل المطلوب.</p>
      </div>
    );
  }

  const handleAssignEngineer = (engineerName: string) => {
    assignEngineerToFault(fault.id, engineerName);
    setAssignDialogOpen(false);
    showSuccess(`تم تعيين المهندس ${engineerName} بنجاح.`);
  };

  const handleStatusChange = (newStatus: FaultStatus) => {
    updateFaultStatus(fault.id, newStatus);
    showSuccess(`تم تحديث حالة العطل إلى "${newStatus}".`);
  };

  const formatBytes = (bytes: number, decimals = 2) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  }

  const availableEngineers = getAvailableEngineers();
  const reportedDate = new Date(fault.reportedAt).toLocaleDateString("ar-EG", {
    year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit'
  });

  return (
    <>
      <AssignEngineerDialog
        open={isAssignDialogOpen}
        onOpenChange={setAssignDialogOpen}
        engineers={availableEngineers}
        onAssignConfirm={handleAssignEngineer}
      />
      <EditFaultDialog
        fault={fault}
        open={isEditDialogOpen}
        onOpenChange={setEditDialogOpen}
      />
      <div className="space-y-6">
        <div className="flex items-center justify-between flex-wrap gap-4">
          <h1 className="text-3xl font-bold">تفاصيل العطل <span className="text-muted-foreground">#{fault.id}</span></h1>
          <div className="flex items-center gap-2 flex-wrap">
            <PrintFaultReport fault={fault} />
            <Button variant="outline" size="sm" onClick={() => setEditDialogOpen(true)}>
              <Pencil className="me-2 h-4 w-4" />
              تعديل
            </Button>
            <UpdateStatusDropdown fault={fault} onStatusChange={handleStatusChange} />
          </div>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card className="lg:col-span-2">
            <CardHeader><CardTitle>وصف المشكلة</CardTitle></CardHeader>
            <CardContent><p className="text-muted-foreground">{fault.description}</p></CardContent>
          </Card>
          
          <Card>
            <CardHeader><CardTitle>معلومات الجهاز</CardTitle></CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center"><Wrench className="me-3 text-muted-foreground" size={20} /><span className="font-semibold">{fault.deviceType}</span></div>
              <div className="flex items-center"><Hash className="me-3 text-muted-foreground" size={20} /><span>{fault.serialNumber || "غير محدد"}</span></div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader><CardTitle>بيانات العميل</CardTitle></CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center"><User className="me-3 text-muted-foreground" size={20} /><span>{fault.customerName}</span></div>
              <div className="flex items-center"><Phone className="me-3 text-muted-foreground" size={20} /><span>{fault.customerPhone}</span></div>
              <div className="flex items-center"><Mail className="me-3 text-muted-foreground" size={20} /><span>{fault.customerEmail}</span></div>
              <div className="flex items-center"><MapPin className="me-3 text-muted-foreground" size={20} /><span>{fault.facilityAddress}</span></div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader><CardTitle>تفاصيل البلاغ</CardTitle></CardHeader>
            <CardContent className="space-y-3">
                <div className="flex items-center">
                    <Badge variant={fault.status === "تم الإصلاح" ? "default" : fault.status === "مفتوح" ? "destructive" : "secondary"}>{fault.status}</Badge>
                </div>
              <div className="flex items-center"><Calendar className="me-3 text-muted-foreground" size={20} /><span>{reportedDate}</span></div>
              <div className="flex items-center"><ShieldAlert className="me-3 text-muted-foreground" size={20} /><Badge variant={fault.priority === "عاجل" ? "destructive" : "secondary"}>{fault.priority}</Badge></div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader><CardTitle>المهندس المسؤول</CardTitle></CardHeader>
            <CardContent>
              {fault.engineer ? (
                <div className="flex items-center"><CheckCircle className="me-3 text-green-500" size={20} /><span>{fault.engineer}</span></div>
              ) : (
                <div className="flex flex-col items-start space-y-3">
                    <p className="text-muted-foreground">لم يتم تعيين مهندس بعد.</p>
                    <Button onClick={() => setAssignDialogOpen(true)}>
                        <UserPlus className="me-2 h-4 w-4" />
                        تعيين مهندس
                    </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {fault.attachments && fault.attachments.length > 0 && (
            <Card className="lg:col-span-3">
              <CardHeader><CardTitle>المرفقات</CardTitle></CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {fault.attachments.map((att, index) => (
                    <li key={index} className="flex items-center justify-between p-2 rounded-md bg-muted">
                      <div className="flex items-center gap-3">
                        <Paperclip className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <p className="text-sm font-medium">{att.name}</p>
                          <p className="text-xs text-muted-foreground">{formatBytes(att.size)}</p>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </>
  );
};

export default FaultDetails;