"use client"

import { useState } from "react"
import { ColumnDef } from "@tanstack/react-table"
import { Link } from "react-router-dom"
import { Fault, FaultPriority, FaultStatus } from "@/types"
import { Badge } from "@/components/ui/badge"
import { MoreH<PERSON>zontal, Trash2, Pencil } from "lucide-react"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuPortal,
} from "@/components/ui/dropdown-menu"
import { useData } from "@/context/DataContext"
import { showSuccess } from "@/utils/toast"
import { possibleStatusTransitions } from "@/lib/fault-utils"
import { ConfirmationDialog } from "../shared/ConfirmationDialog"
import { EditFaultDialog } from "./EditFaultDialog"
import { DataTableColumnHeader } from "../shared/DataTableColumnHeader"

const priorityVariant: Record<FaultPriority, "destructive" | "secondary" | "default"> = {
  "عاجل": "destructive",
  "متوسط": "secondary",
  "عادي": "default",
}

const statusVariant: Record<FaultStatus, "destructive" | "secondary" | "default" | "outline"> = {
    "مفتوح": "destructive",
    "قيد المعالجة": "secondary",
    "تم الإصلاح": "default",
    "لم يتم الإصلاح": "destructive",
    "معلق": "outline",
}

const ActionsCell = ({ fault }: { fault: Fault }) => {
  const { updateFaultStatus, deleteFault } = useData();
  const [isEditDialogOpen, setEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const availableStatuses = possibleStatusTransitions[fault.status] || [];

  const handleStatusChange = (newStatus: FaultStatus) => {
    updateFaultStatus(fault.id, newStatus);
    showSuccess(`تم تحديث حالة العطل إلى "${newStatus}".`);
  };

  const handleDelete = () => {
    deleteFault(fault.id);
    showSuccess("تم حذف العطل بنجاح.");
    setDeleteDialogOpen(false);
  };

  return (
    <>
      <EditFaultDialog 
        fault={fault} 
        open={isEditDialogOpen} 
        onOpenChange={setEditDialogOpen} 
      />
      <ConfirmationDialog
        open={isDeleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleDelete}
        title="هل أنت متأكد؟"
        description="سيتم حذف هذا العطل بشكل دائم. لا يمكن التراجع عن هذا الإجراء."
      />
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">فتح القائمة</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>إجراءات</DropdownMenuLabel>
          <DropdownMenuItem onClick={() => navigator.clipboard.writeText(fault.id)}>
            نسخ الرقم المرجعي
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <Link to={`/faults/${fault.id}`}>عرض التفاصيل</Link>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => setEditDialogOpen(true)}>
            <Pencil className="me-2 h-4 w-4" />
            تعديل البيانات
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          {availableStatuses.length > 0 && (
            <DropdownMenuSub>
              <DropdownMenuSubTrigger>
                <span>تغيير الحالة</span>
              </DropdownMenuSubTrigger>
              <DropdownMenuPortal>
                <DropdownMenuSubContent>
                  {availableStatuses.map(status => (
                    <DropdownMenuItem key={status} onClick={() => handleStatusChange(status)}>
                      تغيير إلى "{status}"
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuSubContent>
              </DropdownMenuPortal>
            </DropdownMenuSub>
          )}
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => setDeleteDialogOpen(true)} className="text-red-600 focus:text-red-600">
            <Trash2 className="me-2 h-4 w-4" />
            حذف
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  )
}

export const columns: ColumnDef<Fault>[] = [
  {
    accessorKey: "id",
    header: ({ column }) => <DataTableColumnHeader column={column} title="الرقم المرجعي" />,
    cell: ({ row }) => {
      const faultId = row.getValue("id") as string;
      return <Link to={`/faults/${faultId}`} className="hover:underline">{faultId}</Link>
    }
  },
  {
    accessorKey: "customerName",
    header: ({ column }) => <DataTableColumnHeader column={column} title="اسم العميل" />,
  },
  {
    accessorKey: "deviceType",
    header: ({ column }) => <DataTableColumnHeader column={column} title="نوع الجهاز" />,
  },
  {
    accessorKey: "priority",
    header: ({ column }) => <DataTableColumnHeader column={column} title="الأولوية" />,
    cell: ({ row }) => {
      const priority = row.getValue("priority") as FaultPriority
      return <Badge variant={priorityVariant[priority] || "default"}>{priority}</Badge>
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    accessorKey: "status",
    header: ({ column }) => <DataTableColumnHeader column={column} title="الحالة" />,
    cell: ({ row }) => {
        const status = row.getValue("status") as FaultStatus
        return <Badge variant={statusVariant[status] || "default"}>{status}</Badge>
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    accessorKey: "engineer",
    header: ({ column }) => <DataTableColumnHeader column={column} title="المهندس" />,
    cell: ({ row }) => {
      const engineerName = row.original.engineer;
      const { engineers } = useData();

      if (!engineerName) {
        return <span className="text-muted-foreground text-center w-full block">-</span>;
      }

      const engineer = engineers.find(e => e.name === engineerName);

      if (engineer) {
        return <Link to={`/engineers/${engineer.id}`} className="hover:underline">{engineer.name}</Link>
      }

      return <span>{engineerName}</span>;
    },
  },
  {
    accessorKey: "reportedAt",
    header: ({ column }) => <DataTableColumnHeader column={column} title="تاريخ الإبلاغ" />,
    cell: ({ row }) => {
      const date = new Date(row.getValue("reportedAt"))
      return <div className="min-w-max">{date.toLocaleDateString("ar-EG", { year: 'numeric', month: 'short', day: 'numeric' })}</div>
    },
  },
  {
    accessorKey: "completedAt",
    header: ({ column }) => <DataTableColumnHeader column={column} title="تاريخ الإنجاز" />,
    cell: ({ row }) => {
      const completedAt = row.original.completedAt;
      if (!completedAt) return <span className="text-muted-foreground text-center w-full block">-</span>;
      const date = new Date(completedAt);
      return <div className="min-w-max">{date.toLocaleDateString("ar-EG", { year: 'numeric', month: 'short', day: 'numeric' })}</div>
    },
  },
  {
    id: "actions",
    cell: ({ row }) => <ActionsCell fault={row.original} />,
  },
]