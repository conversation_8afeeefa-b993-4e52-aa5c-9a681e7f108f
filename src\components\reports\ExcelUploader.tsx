import { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Upload, FileSpreadsheet, Download, AlertCircle, CheckCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface ExcelData {
  headers: string[];
  rows: any[][];
  fileName: string;
}

export function ExcelUploader() {
  const [excelData, setExcelData] = useState<ExcelData | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleCSVFile = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const text = e.target?.result as string;
        const lines = text.split('\n').filter(line => line.trim());

        if (lines.length === 0) {
          setError("الملف فارغ أو لا يحتوي على بيانات صالحة");
          return;
        }

        const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
        const rows = lines.slice(1).map(line =>
          line.split(',').map(cell => cell.trim().replace(/"/g, ''))
        );

        setExcelData({
          headers,
          rows: rows.filter(row => row.some(cell => cell !== "")),
          fileName: file.name
        });

      } catch (err) {
        setError("حدث خطأ أثناء قراءة الملف. تأكد من أن الملف بتنسيق CSV صحيح.");
      } finally {
        setIsProcessing(false);
      }
    };

    reader.readAsText(file);
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // التحقق من نوع الملف
    if (!file.name.match(/\.csv$/)) {
      setError("يرجى اختيار ملف CSV صالح (.csv)");
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      // معالجة ملف CSV
      handleCSVFile(file);
    } catch (err) {
      setError("حدث خطأ أثناء معالجة الملف");
      setIsProcessing(false);
    }
  };

  const generateReport = () => {
    if (!excelData) return;

    const printWindow = window.open('', '_blank');
    if (!printWindow) return;

    printWindow.document.write(`
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <title>تقرير من ملف Excel - ${excelData.fileName}</title>
        <style>
          body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            direction: rtl; 
            line-height: 1.6;
          }
          .header { 
            text-align: center; 
            margin-bottom: 30px; 
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
          }
          .file-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
          }
          table { 
            width: 100%; 
            border-collapse: collapse; 
            margin-top: 20px; 
            font-size: 12px;
          }
          th, td { 
            border: 1px solid #ddd; 
            padding: 8px; 
            text-align: right; 
          }
          th { 
            background-color: #f3f4f6; 
            font-weight: bold;
          }
          tr:nth-child(even) {
            background-color: #f9f9f9;
          }
          .stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 20px;
          }
          .stat-card {
            background: #f9fafb;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e5e7eb;
          }
          .footer {
            margin-top: 40px;
            text-align: center;
            color: #666;
            font-size: 12px;
            border-top: 1px solid #ddd;
            padding-top: 20px;
          }
          @media print { 
            body { margin: 0; }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>تقرير البيانات المستوردة</h1>
          <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
        </div>
        
        <div class="file-info">
          <h3>معلومات الملف</h3>
          <p><strong>اسم الملف:</strong> ${excelData.fileName}</p>
          <p><strong>عدد الأعمدة:</strong> ${excelData.headers.length}</p>
          <p><strong>عدد الصفوف:</strong> ${excelData.rows.length}</p>
        </div>

        <div class="stats">
          <div class="stat-card">
            <h4>إجمالي البيانات</h4>
            <p style="font-size: 24px; font-weight: bold; color: #1f2937;">${excelData.rows.length}</p>
          </div>
          <div class="stat-card">
            <h4>عدد الأعمدة</h4>
            <p style="font-size: 24px; font-weight: bold; color: #1f2937;">${excelData.headers.length}</p>
          </div>
          <div class="stat-card">
            <h4>حالة البيانات</h4>
            <p style="font-size: 16px; font-weight: bold; color: #059669;">مكتملة</p>
          </div>
        </div>

        <h2>البيانات المستوردة</h2>
        <table>
          <thead>
            <tr>
              ${excelData.headers.map(header => `<th>${header || 'عمود فارغ'}</th>`).join('')}
            </tr>
          </thead>
          <tbody>
            ${excelData.rows.map(row => `
              <tr>
                ${excelData.headers.map((_, index) => `<td>${row[index] || '-'}</td>`).join('')}
              </tr>
            `).join('')}
          </tbody>
        </table>

        <div class="footer">
          <p>تم إنشاء هذا التقرير بواسطة نظام إدارة الأعطال</p>
          <p>البيانات مستوردة من ملف Excel</p>
        </div>
      </body>
      </html>
    `);
    
    printWindow.document.close();
    printWindow.focus();
    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 250);
  };

  const clearData = () => {
    setExcelData(null);
    setError(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="space-y-6">
      {/* منطقة رفع الملف */}
      <Card className="modern-card border-2 border-dashed border-primary/30 hover:border-primary/50 transition-all duration-300">
        <CardHeader className="text-center">
          <CardTitle className="flex items-center justify-center gap-2">
            <FileSpreadsheet className="h-6 w-6 text-primary" />
            رفع ملف CSV
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <div className="flex flex-col items-center gap-4">
            <div className="p-8 rounded-full bg-primary/10">
              <Upload className="h-12 w-12 text-primary" />
            </div>

            <div>
              <p className="text-muted-foreground mb-2">
                اختر ملف CSV لإنشاء تقرير عليه
              </p>
              <p className="text-xs text-muted-foreground">
                يدعم ملفات .csv فقط
              </p>
            </div>

            <input
              ref={fileInputRef}
              type="file"
              accept=".csv"
              onChange={handleFileUpload}
              className="hidden"
              disabled={isProcessing}
            />
            
            <Button 
              onClick={() => fileInputRef.current?.click()}
              disabled={isProcessing}
              className="modern-button"
            >
              {isProcessing ? (
                <>
                  <Upload className="h-4 w-4 animate-spin mr-2" />
                  جاري المعالجة...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4 mr-2" />
                  اختيار ملف
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* رسائل الخطأ */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* معاينة البيانات */}
      {excelData && (
        <Card className="modern-card">
          <CardHeader className="flex flex-row items-center justify-between">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <CardTitle>تم تحميل الملف بنجاح</CardTitle>
            </div>
            <div className="flex gap-2">
              <Button onClick={generateReport} className="modern-button">
                <Download className="h-4 w-4 mr-2" />
                طباعة التقرير
              </Button>
              <Button variant="outline" onClick={clearData}>
                مسح البيانات
              </Button>
            </div>
          </CardHeader>
          
          <CardContent className="space-y-4">
            {/* معلومات الملف */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-muted/50 rounded-lg">
                <p className="text-sm text-muted-foreground">اسم الملف</p>
                <p className="font-medium">{excelData.fileName}</p>
              </div>
              <div className="text-center p-4 bg-muted/50 rounded-lg">
                <p className="text-sm text-muted-foreground">عدد الصفوف</p>
                <p className="font-medium text-primary">{excelData.rows.length}</p>
              </div>
              <div className="text-center p-4 bg-muted/50 rounded-lg">
                <p className="text-sm text-muted-foreground">عدد الأعمدة</p>
                <p className="font-medium text-accent">{excelData.headers.length}</p>
              </div>
            </div>

            {/* معاينة الأعمدة */}
            <div>
              <h4 className="font-medium mb-2">أعمدة البيانات:</h4>
              <div className="flex flex-wrap gap-2">
                {excelData.headers.map((header, index) => (
                  <Badge key={index} variant="secondary">
                    {header || `عمود ${index + 1}`}
                  </Badge>
                ))}
              </div>
            </div>

            {/* معاينة البيانات */}
            <div>
              <h4 className="font-medium mb-2">معاينة البيانات (أول 5 صفوف):</h4>
              <div className="overflow-x-auto">
                <table className="w-full text-sm border-collapse border border-border">
                  <thead>
                    <tr className="bg-muted">
                      {excelData.headers.map((header, index) => (
                        <th key={index} className="border border-border p-2 text-right">
                          {header || `عمود ${index + 1}`}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {excelData.rows.slice(0, 5).map((row, rowIndex) => (
                      <tr key={rowIndex} className="hover:bg-muted/50">
                        {excelData.headers.map((_, colIndex) => (
                          <td key={colIndex} className="border border-border p-2">
                            {row[colIndex] || '-'}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              {excelData.rows.length > 5 && (
                <p className="text-xs text-muted-foreground mt-2">
                  ... و {excelData.rows.length - 5} صف إضافي
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
