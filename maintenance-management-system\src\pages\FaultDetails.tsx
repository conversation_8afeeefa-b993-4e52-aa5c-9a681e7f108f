import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, User, Phone, Mail, MapPin, Calendar, Wrench, Clock, CheckCircle, XCircle, Video } from "lucide-react";
import { useData } from "@/context/DataContext";
import { FaultStatus } from "@/types";

const FaultDetails = () => {
  const { id } = useParams<{ id: string }>();
  const { faults } = useData();
  
  const fault = faults.find(f => f.id === id);

  if (!fault) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Link to="/faults">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 ml-2" />
              العودة للأعطال
            </Button>
          </Link>
        </div>
        <Card>
          <CardContent className="text-center py-12">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              العطل غير موجود
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              لم يتم العثور على العطل المطلوب
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const getStatusIcon = (status: FaultStatus) => {
    switch (status) {
      case "مفتوح":
        return <Clock className="h-5 w-5" />;
      case "قيد المعالجة":
        return <Wrench className="h-5 w-5" />;
      case "تم الإصلاح":
        return <CheckCircle className="h-5 w-5" />;
      case "لم يتم الإصلاح":
        return <XCircle className="h-5 w-5" />;
      default:
        return <Clock className="h-5 w-5" />;
    }
  };

  const getStatusColor = (status: FaultStatus) => {
    switch (status) {
      case "مفتوح":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "قيد المعالجة":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "تم الإصلاح":
        return "bg-green-100 text-green-800 border-green-200";
      case "لم يتم الإصلاح":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "عاجل":
        return "bg-red-100 text-red-800 border-red-200";
      case "متوسط":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "عادي":
        return "bg-green-100 text-green-800 border-green-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link to="/faults">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 ml-2" />
              العودة للأعطال
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              تفاصيل العطل {fault.id}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              {fault.customerName} • {fault.deviceType}
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Badge className={`${getPriorityColor(fault.priority)} border`}>
            {fault.priority}
          </Badge>
          <Badge className={`${getStatusColor(fault.status)} border flex items-center gap-1`}>
            {getStatusIcon(fault.status)}
            {fault.status}
          </Badge>
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* معلومات العميل */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              معلومات العميل
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-semibold text-lg">{fault.customerName}</h3>
            </div>
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-sm">
                <Phone className="h-4 w-4 text-gray-500" />
                <span>{fault.customerPhone}</span>
              </div>
              {fault.customerEmail && (
                <div className="flex items-center gap-2 text-sm">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <span>{fault.customerEmail}</span>
                </div>
              )}
              <div className="flex items-center gap-2 text-sm">
                <MapPin className="h-4 w-4 text-gray-500" />
                <span>{fault.facilityAddress}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* معلومات الجهاز */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Wrench className="h-5 w-5" />
              معلومات الجهاز
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">نوع الجهاز</label>
              <p className="text-lg">{fault.deviceType}</p>
            </div>
            {fault.serialNumber && (
              <div>
                <label className="text-sm font-medium text-gray-500">الرقم التسلسلي</label>
                <p>{fault.serialNumber}</p>
              </div>
            )}
            <div>
              <label className="text-sm font-medium text-gray-500">وصف العطل</label>
              <p className="text-sm leading-relaxed">{fault.faultDescription}</p>
            </div>
          </CardContent>
        </Card>

        {/* معلومات الصيانة */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              معلومات الصيانة
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">تاريخ الإبلاغ</label>
              <p>{new Date(fault.reportedAt).toLocaleDateString('ar-EG')}</p>
            </div>
            {fault.engineer && (
              <div>
                <label className="text-sm font-medium text-gray-500">المهندس المسؤول</label>
                <p>{fault.engineer}</p>
              </div>
            )}
            {fault.completedAt && (
              <div>
                <label className="text-sm font-medium text-gray-500">تاريخ الإكمال</label>
                <p>{new Date(fault.completedAt).toLocaleDateString('ar-EG')}</p>
              </div>
            )}
            {fault.notes && (
              <div>
                <label className="text-sm font-medium text-gray-500">ملاحظات</label>
                <p className="text-sm">{fault.notes}</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* فيديوهات الصيانة */}
      {((fault.videosBeforeMaintenance && fault.videosBeforeMaintenance.length > 0) || 
        (fault.videosAfterMaintenance && fault.videosAfterMaintenance.length > 0)) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Video className="h-5 w-5" />
              فيديوهات الصيانة
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 md:grid-cols-2">
              {/* فيديوهات قبل الصيانة */}
              {fault.videosBeforeMaintenance && fault.videosBeforeMaintenance.length > 0 && (
                <div>
                  <h4 className="font-medium mb-3 text-blue-600">فيديوهات قبل الصيانة</h4>
                  <div className="space-y-2">
                    {fault.videosBeforeMaintenance.map((video) => (
                      <div key={video.id} className="p-3 border rounded-lg">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-sm">{video.name}</p>
                            <p className="text-xs text-gray-500">
                              {new Date(video.uploadedAt).toLocaleDateString('ar-EG')} • 
                              {(video.size / 1024 / 1024).toFixed(2)} MB
                            </p>
                          </div>
                          <Button variant="outline" size="sm">
                            مشاهدة
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* فيديوهات بعد الصيانة */}
              {fault.videosAfterMaintenance && fault.videosAfterMaintenance.length > 0 && (
                <div>
                  <h4 className="font-medium mb-3 text-green-600">فيديوهات بعد الصيانة</h4>
                  <div className="space-y-2">
                    {fault.videosAfterMaintenance.map((video) => (
                      <div key={video.id} className="p-3 border rounded-lg">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-sm">{video.name}</p>
                            <p className="text-xs text-gray-500">
                              {new Date(video.uploadedAt).toLocaleDateString('ar-EG')} • 
                              {(video.size / 1024 / 1024).toFixed(2)} MB
                            </p>
                          </div>
                          <Button variant="outline" size="sm">
                            مشاهدة
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* المرفقات */}
      {fault.attachments && fault.attachments.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>المرفقات</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-3">
              {fault.attachments.map((attachment, index) => (
                <div key={index} className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-sm">{attachment.name}</p>
                      <p className="text-xs text-gray-500">
                        {(attachment.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                    <Button variant="outline" size="sm">
                      تحميل
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default FaultDetails;
