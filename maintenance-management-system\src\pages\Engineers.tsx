import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus, Search, Filter, Eye, User, Phone, Mail, MapPin } from "lucide-react";
import { useData } from "@/context/DataContext";
import { Engineer, EngineerStatus, EngineerSpecialty } from "@/types";

const Engineers = () => {
  const { engineers } = useData();
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<EngineerStatus | "all">("all");
  const [specialtyFilter, setSpecialtyFilter] = useState<EngineerSpecialty | "all">("all");

  // تصفية المهندسين
  const filteredEngineers = engineers.filter((engineer) => {
    const matchesSearch = 
      engineer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      engineer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      engineer.area.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === "all" || engineer.status === statusFilter;
    const matchesSpecialty = specialtyFilter === "all" || engineer.specialty === specialtyFilter;
    
    return matchesSearch && matchesStatus && matchesSpecialty;
  });

  const getStatusColor = (status: EngineerStatus) => {
    switch (status) {
      case "متاح":
        return "bg-green-100 text-green-800 border-green-200";
      case "مشغول":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "في إجازة":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getSpecialtyColor = (specialty: EngineerSpecialty) => {
    switch (specialty) {
      case "أجهزة ليزر":
        return "bg-purple-100 text-purple-800 border-purple-200";
      case "أجهزة تخسيس":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "عام":
        return "bg-gray-100 text-gray-800 border-gray-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">المهندسون</h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            إدارة ومتابعة فريق المهندسين
          </p>
        </div>
        <Button className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          إضافة مهندس جديد
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            البحث والتصفية
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="البحث بالاسم أو البريد الإلكتروني أو المنطقة..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10"
              />
            </div>
            
            <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as EngineerStatus | "all")}>
              <SelectTrigger>
                <SelectValue placeholder="تصفية حسب الحالة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الحالات</SelectItem>
                <SelectItem value="متاح">متاح</SelectItem>
                <SelectItem value="مشغول">مشغول</SelectItem>
                <SelectItem value="في إجازة">في إجازة</SelectItem>
              </SelectContent>
            </Select>

            <Select value={specialtyFilter} onValueChange={(value) => setSpecialtyFilter(value as EngineerSpecialty | "all")}>
              <SelectTrigger>
                <SelectValue placeholder="تصفية حسب التخصص" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع التخصصات</SelectItem>
                <SelectItem value="أجهزة ليزر">أجهزة ليزر</SelectItem>
                <SelectItem value="أجهزة تخسيس">أجهزة تخسيس</SelectItem>
                <SelectItem value="عام">عام</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            عرض {filteredEngineers.length} من أصل {engineers.length} مهندس
          </p>
        </div>

        {filteredEngineers.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <User className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                لا يوجد مهندسون
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                لم يتم العثور على مهندسين يطابقون معايير البحث
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {filteredEngineers.map((engineer) => (
              <Card key={engineer.id} className="hover:shadow-lg transition-shadow duration-200">
                <CardContent className="p-6">
                  <div className="space-y-4">
                    {/* Header */}
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                          <User className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                            {engineer.name}
                          </h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {engineer.id}
                          </p>
                        </div>
                      </div>
                      <div className="flex flex-col gap-2">
                        <Badge className={`${getStatusColor(engineer.status)} border text-xs`}>
                          {engineer.status}
                        </Badge>
                        <Badge className={`${getSpecialtyColor(engineer.specialty)} border text-xs`}>
                          {engineer.specialty}
                        </Badge>
                      </div>
                    </div>

                    {/* Contact Info */}
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                        <Phone className="h-4 w-4" />
                        <span>{engineer.phone}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                        <Mail className="h-4 w-4" />
                        <span className="truncate">{engineer.email}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                        <MapPin className="h-4 w-4" />
                        <span>{engineer.area}</span>
                      </div>
                    </div>

                    {/* Join Date */}
                    <div className="text-xs text-gray-500 dark:text-gray-400 border-t pt-3">
                      انضم في: {new Date(engineer.joinedAt).toLocaleDateString('ar-EG')}
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2 pt-2">
                      <Link to={`/engineers/${engineer.id}`} className="flex-1">
                        <Button variant="outline" size="sm" className="w-full flex items-center gap-2">
                          <Eye className="h-4 w-4" />
                          عرض التفاصيل
                        </Button>
                      </Link>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default Engineers;
