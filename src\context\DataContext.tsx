import { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import { <PERSON><PERSON>, Engineer, FaultStatus, Attachment, VideoAttachment } from '@/types';
import { initialFaultsData, initialEngineersData } from '@/lib/mock-data';

type FaultUpdateData = Omit<Fault, 'id' | 'status' | 'reportedAt' | 'completedAt' | 'engineer'>;
type EngineerUpdateData = Omit<Engineer, 'id' | 'status'>;

// Define the shape of the context data
interface DataContextType {
  faults: Fault[];
  engineers: Engineer[];
  addFault: (faultData: Omit<Fault, 'id' | 'status' | 'reportedAt' | 'engineer' | 'completedAt'>) => void;
  updateFault: (faultId: string, updatedData: FaultUpdateData) => void;
  addEngineer: (engineerData: Omit<Engineer, 'id' | 'status'>) => void;
  assignEngineerToFault: (faultId: string, engineerName: string) => void;
  updateFaultStatus: (faultId: string, newStatus: FaultStatus) => void;
  getAvailableEngineers: () => Engineer[];
  updateEngineer: (engineerId: string, updatedData: EngineerUpdateData) => void;
  deleteFault: (faultId: string) => void;
  deleteEngineer: (engineerId: string) => void;
  addVideoToFault: (faultId: string, video: VideoAttachment) => void;
  removeVideoFromFault: (faultId: string, videoId: string) => void;
  updateFaultVideos: (faultId: string, videosBeforeMaintenance?: VideoAttachment[], videosAfterMaintenance?: VideoAttachment[]) => void;
}

// Create the context
const DataContext = createContext<DataContextType | undefined>(undefined);

// Helper to get initial state from localStorage or fallback to mock data
const getInitialState = <T,>(key: string, fallback: T): T => {
    try {
        const storedItem = localStorage.getItem(key);
        if (storedItem) {
            return JSON.parse(storedItem);
        }
    } catch (error) {
        console.error(`Error reading from localStorage key “${key}”:`, error);
    }
    return fallback;
};

// Create the provider component
export const DataProvider = ({ children }: { children: ReactNode }) => {
  const [faults, setFaults] = useState<Fault[]>(() => getInitialState('app_faults', initialFaultsData));
  const [engineers, setEngineers] = useState<Engineer[]>(() => getInitialState('app_engineers', initialEngineersData));

  // Effect to save faults to localStorage whenever they change
  useEffect(() => {
    try {
        localStorage.setItem('app_faults', JSON.stringify(faults));
    } catch (error) {
        console.error('Failed to save faults to localStorage:', error);
    }
  }, [faults]);

  // Effect to save engineers to localStorage whenever they change
  useEffect(() => {
    try {
        localStorage.setItem('app_engineers', JSON.stringify(engineers));
    } catch (error) {
        console.error('Failed to save engineers to localStorage:', error);
    }
  }, [engineers]);


  const addFault = (faultData: Omit<Fault, 'id' | 'status' | 'reportedAt' | 'engineer' | 'completedAt'>) => {
    const newFault: Fault = {
      ...faultData,
      id: `F-${Date.now().toString().slice(-6)}`,
      status: 'مفتوح',
      reportedAt: new Date().toISOString(),
    };
    setFaults(prev => [newFault, ...prev]);
  };

  const updateFault = (faultId: string, updatedData: FaultUpdateData) => {
    setFaults(prev =>
        prev.map(f =>
            f.id === faultId ? { ...f, ...updatedData } : f
        )
    );
  };

  const addEngineer = (engineerData: Omit<Engineer, 'id' | 'status'>) => {
    const newEngineer: Engineer = {
      ...engineerData,
      id: `E-${Date.now().toString().slice(-6)}`,
      status: 'متاح',
    };
    setEngineers(prev => [newEngineer, ...prev]);
  };

  const assignEngineerToFault = (faultId: string, engineerName: string) => {
    setFaults(prevFaults =>
      prevFaults.map(fault =>
        fault.id === faultId
          ? { ...fault, engineer: engineerName, status: 'قيد المعالجة' }
          : fault
      )
    );
    setEngineers(prevEngineers =>
        prevEngineers.map(eng =>
            eng.name === engineerName ? { ...eng, status: 'مشغول' } : eng
        )
    );
  };
  
  const updateFaultStatus = (faultId: string, newStatus: FaultStatus) => {
    let engineerToFree: string | undefined;
    
    setFaults(prevFaults =>
      prevFaults.map(fault => {
        if (fault.id === faultId) {
          // Free up engineer if status changes to a final state
          if (fault.engineer && (newStatus === 'تم الإصلاح' || newStatus === 'لم يتم الإصلاح')) {
            engineerToFree = fault.engineer;
          }

          const updatedFault: Fault = { ...fault, status: newStatus };
          
          // Set completion date if status is 'تم الإصلاح', otherwise remove it.
          if (newStatus === 'تم الإصلاح') {
            updatedFault.completedAt = new Date().toISOString();
          } else {
            delete updatedFault.completedAt;
          }
          
          return updatedFault;
        }
        return fault;
      })
    );

    if (engineerToFree) {
        setEngineers(prevEngineers =>
            prevEngineers.map(eng =>
                eng.name === engineerToFree ? { ...eng, status: 'متاح' } : eng
            )
        );
    }
  };

  const updateEngineer = (engineerId: string, updatedData: EngineerUpdateData) => {
    setEngineers(prev =>
        prev.map(eng =>
            eng.id === engineerId ? { ...eng, ...updatedData } : eng
        )
    );
  };

  const deleteFault = (faultId: string) => {
    // If the fault had an engineer, their status should be updated if they have no other active faults.
    const faultToDelete = faults.find(f => f.id === faultId);
    if (faultToDelete?.engineer) {
        const otherFaults = faults.filter(f => f.id !== faultId && f.engineer === faultToDelete.engineer && f.status === 'قيد المعالجة');
        if (otherFaults.length === 0) {
            setEngineers(prev => prev.map(eng => eng.name === faultToDelete.engineer ? { ...eng, status: 'متاح' } : eng));
        }
    }
    setFaults(prev => prev.filter(f => f.id !== faultId));
  };

  const deleteEngineer = (engineerId: string) => {
    const engineerToDelete = engineers.find(e => e.id === engineerId);
    if (!engineerToDelete) return;

    // Un-assign the engineer from any faults and set status to 'Open'
    setFaults(prevFaults => 
        prevFaults.map(fault => 
            fault.engineer === engineerToDelete.name 
            ? { ...fault, engineer: undefined, status: 'مفتوح' } 
            : fault
        )
    );
    setEngineers(prev => prev.filter(e => e.id !== engineerId));
  };

  const getAvailableEngineers = () => {
    return engineers.filter(e => e.status === "متاح");
  }

  const addVideoToFault = (faultId: string, video: VideoAttachment) => {
    setFaults(prevFaults =>
      prevFaults.map(fault => {
        if (fault.id === faultId) {
          if (video.type === 'before') {
            return {
              ...fault,
              videosBeforeMaintenance: [...(fault.videosBeforeMaintenance || []), video]
            };
          } else {
            return {
              ...fault,
              videosAfterMaintenance: [...(fault.videosAfterMaintenance || []), video]
            };
          }
        }
        return fault;
      })
    );
  };

  const removeVideoFromFault = (faultId: string, videoId: string) => {
    setFaults(prevFaults =>
      prevFaults.map(fault => {
        if (fault.id === faultId) {
          return {
            ...fault,
            videosBeforeMaintenance: fault.videosBeforeMaintenance?.filter(v => v.id !== videoId) || [],
            videosAfterMaintenance: fault.videosAfterMaintenance?.filter(v => v.id !== videoId) || []
          };
        }
        return fault;
      })
    );
  };

  const updateFaultVideos = (faultId: string, videosBeforeMaintenance?: VideoAttachment[], videosAfterMaintenance?: VideoAttachment[]) => {
    setFaults(prevFaults =>
      prevFaults.map(fault => {
        if (fault.id === faultId) {
          return {
            ...fault,
            ...(videosBeforeMaintenance !== undefined && { videosBeforeMaintenance }),
            ...(videosAfterMaintenance !== undefined && { videosAfterMaintenance })
          };
        }
        return fault;
      })
    );
  };

  const value = {
    faults,
    engineers,
    addFault,
    updateFault,
    addEngineer,
    assignEngineerToFault,
    getAvailableEngineers,
    updateFaultStatus,
    updateEngineer,
    deleteFault,
    deleteEngineer,
    addVideoToFault,
    removeVideoFromFault,
    updateFaultVideos
  };

  return <DataContext.Provider value={value}>{children}</DataContext.Provider>;
};

// Create a custom hook for easy context access
export const useData = () => {
  const context = useContext(DataContext);
  if (context === undefined) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};