import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  <PERSON>alogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Engineer, EngineerSpecialty } from "@/types";
import { showSuccess } from "@/utils/toast";
import { useData } from "../../context/DataContext";

const specialties: [EngineerSpecialty, ...EngineerSpecialty[]] = ["أجهزة ليزر", "أجهزة تخسيس", "عام"];

const formSchema = z.object({
  name: z.string().min(2, { message: "الاسم مطلوب." }),
  phone: z.string().min(10, { message: "رقم هاتف صالح مطلوب." }),
  email: z.string().email({ message: "بريد إلكتروني غير صالح." }),
  area: z.string().min(2, { message: "المنطقة مطلوبة." }),
  specialty: z.enum(specialties, { required_error: "الرجاء اختيار التخصص." }),
});

interface EditEngineerDialogProps {
  engineer: Engineer;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function EditEngineerDialog({ engineer, open, onOpenChange }: EditEngineerDialogProps) {
  const { updateEngineer } = useData();
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: engineer,
  });

  useEffect(() => {
    form.reset(engineer);
  }, [engineer, form]);

  function onSubmit(values: z.infer<typeof formSchema>) {
    updateEngineer(engineer.id, values as Omit<Engineer, 'id' | 'status'>);
    showSuccess("تم تحديث بيانات المهندس بنجاح!");
    onOpenChange(false);
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[525px]">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <DialogHeader>
              <DialogTitle>تعديل بيانات المهندس</DialogTitle>
              <DialogDescription>
                قم بتحديث المعلومات المطلوبة ثم اضغط على حفظ.
              </DialogDescription>
            </DialogHeader>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-4">
              <FormField control={form.control} name="name" render={({ field }) => (
                <FormItem><FormLabel>اسم المهندس</FormLabel><FormControl><Input {...field} /></FormControl><FormMessage /></FormItem>
              )} />
              <FormField control={form.control} name="phone" render={({ field }) => (
                <FormItem><FormLabel>رقم الهاتف</FormLabel><FormControl><Input {...field} /></FormControl><FormMessage /></FormItem>
              )} />
              <FormField control={form.control} name="email" render={({ field }) => (
                <FormItem className="md:col-span-2"><FormLabel>البريد الإلكتروني</FormLabel><FormControl><Input {...field} /></FormControl><FormMessage /></FormItem>
              )} />
              <FormField control={form.control} name="specialty" render={({ field }) => (
                <FormItem><FormLabel>التخصص</FormLabel><Select onValueChange={field.onChange} defaultValue={field.value}><FormControl><SelectTrigger><SelectValue /></SelectTrigger></FormControl><SelectContent>{specialties.map(type => <SelectItem key={type} value={type}>{type}</SelectItem>)}</SelectContent></Select><FormMessage /></FormItem>
              )} />
               <FormField control={form.control} name="area" render={({ field }) => (
                <FormItem><FormLabel>المنطقة الجغرافية</FormLabel><FormControl><Input {...field} /></FormControl><FormMessage /></FormItem>
              )} />
            </div>
            <DialogFooter className="pt-4">
              <DialogClose asChild><Button type="button" variant="secondary">إلغاء</Button></DialogClose>
              <Button type="submit">حفظ التغييرات</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}