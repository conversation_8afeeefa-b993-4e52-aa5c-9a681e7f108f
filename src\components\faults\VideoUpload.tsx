import { useState, useRef } from "react";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { VideoAttachment } from "@/types";
import { Upload, Video, Trash2, Play, Download } from "lucide-react";
import { showSuccess, showError } from "@/utils/toast";

interface VideoUploadProps {
  videos: VideoAttachment[];
  onVideosChange: (videos: VideoAttachment[]) => void;
  type: 'before' | 'after';
  title: string;
  description: string;
  disabled?: boolean;
}

export function VideoUpload({ 
  videos, 
  onVideosChange, 
  type, 
  title, 
  description, 
  disabled = false 
}: VideoUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    setIsUploading(true);
    
    try {
      const newVideos: VideoAttachment[] = [];
      
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        
        // التحقق من نوع الملف
        if (!file.type.startsWith('video/')) {
          showError(`الملف ${file.name} ليس فيديو صالح`);
          continue;
        }

        // التحقق من حجم الملف (100MB كحد أقصى)
        if (file.size > 100 * 1024 * 1024) {
          showError(`الملف ${file.name} كبير جداً. الحد الأقصى 100MB`);
          continue;
        }

        // إنشاء URL للفيديو (في التطبيق الحقيقي سيتم رفعه للخادم)
        const videoUrl = URL.createObjectURL(file);
        
        const newVideo: VideoAttachment = {
          id: `video-${Date.now()}-${i}`,
          name: file.name,
          url: videoUrl,
          type: type,
          uploadedAt: new Date().toISOString(),
          size: file.size
        };

        newVideos.push(newVideo);
      }

      if (newVideos.length > 0) {
        onVideosChange([...videos, ...newVideos]);
        showSuccess(`تم رفع ${newVideos.length} فيديو بنجاح`);
      }
    } catch (error) {
      showError('حدث خطأ أثناء رفع الفيديو');
    } finally {
      setIsUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleRemoveVideo = (videoId: string) => {
    const updatedVideos = videos.filter(v => v.id !== videoId);
    onVideosChange(updatedVideos);
    showSuccess('تم حذف الفيديو');
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('ar-EG');
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Video className="h-5 w-5" />
          {title}
        </CardTitle>
        <p className="text-sm text-muted-foreground">{description}</p>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* زر رفع الفيديو */}
        <div className="flex flex-col items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg p-6">
          <input
            ref={fileInputRef}
            type="file"
            accept="video/*"
            multiple
            onChange={handleFileSelect}
            className="hidden"
            disabled={disabled || isUploading}
          />
          <Video className="h-12 w-12 text-muted-foreground mb-4" />
          <Button
            onClick={() => fileInputRef.current?.click()}
            disabled={disabled || isUploading}
            className="mb-2"
          >
            <Upload className="h-4 w-4 me-2" />
            {isUploading ? 'جاري الرفع...' : 'رفع فيديو'}
          </Button>
          <p className="text-xs text-muted-foreground text-center">
            يمكن رفع ملفات فيديو بحجم أقصى 100MB
            <br />
            الصيغ المدعومة: MP4, AVI, MOV, WMV
          </p>
        </div>

        {/* قائمة الفيديوهات المرفوعة */}
        {videos.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium">الفيديوهات المرفوعة ({videos.length})</h4>
            {videos.map((video) => (
              <div key={video.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3 flex-1">
                  <Video className="h-8 w-8 text-blue-500" />
                  <div className="flex-1 min-w-0">
                    <p className="font-medium truncate">{video.name}</p>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <span>{formatFileSize(video.size)}</span>
                      <span>•</span>
                      <span>{formatDate(video.uploadedAt)}</span>
                    </div>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {type === 'before' ? 'قبل الصيانة' : 'بعد الصيانة'}
                  </Badge>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const link = document.createElement('a');
                      link.href = video.url;
                      link.download = video.name;
                      link.click();
                    }}
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => window.open(video.url, '_blank')}
                  >
                    <Play className="h-4 w-4" />
                  </Button>
                  {!disabled && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRemoveVideo(video.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
