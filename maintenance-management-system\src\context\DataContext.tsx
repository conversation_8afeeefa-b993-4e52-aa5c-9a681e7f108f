import React, { createContext, useContext, useState, ReactNode } from 'react';
import { <PERSON><PERSON>, Engineer, FaultStatus, Attachment, VideoAttachment } from '@/types';
import { mockFaults, mockEngineers } from '@/lib/mock-data';

interface DataContextType {
  faults: Fault[];
  engineers: Engineer[];
  addFault: (fault: Omit<Fault, 'id' | 'reportedAt'>) => void;
  updateFault: (faultId: string, updatedData: Partial<Fault>) => void;
  addEngineer: (engineer: Omit<Engineer, 'id' | 'joinedAt'>) => void;
  assignEngineerToFault: (faultId: string, engineerName: string) => void;
  getAvailableEngineers: () => Engineer[];
  updateFaultStatus: (faultId: string, status: FaultStatus) => void;
  updateEngineer: (engineerId: string, updatedData: EngineerUpdateData) => void;
  deleteFault: (faultId: string) => void;
  deleteEngineer: (engineerId: string) => void;
  addVideoToFault: (faultId: string, video: VideoAttachment) => void;
  removeVideoFromFault: (faultId: string, videoId: string) => void;
  updateFaultVideos: (faultId: string, videosBeforeMaintenance?: VideoAttachment[], videosAfterMaintenance?: VideoAttachment[]) => void;
}

interface EngineerUpdateData {
  name?: string;
  phone?: string;
  email?: string;
  specialty?: Engineer['specialty'];
  status?: Engineer['status'];
  area?: string;
}

const DataContext = createContext<DataContextType | undefined>(undefined);

export const useData = () => {
  const context = useContext(DataContext);
  if (context === undefined) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};

interface DataProviderProps {
  children: ReactNode;
}

export const DataProvider: React.FC<DataProviderProps> = ({ children }) => {
  const [faults, setFaults] = useState<Fault[]>(mockFaults);
  const [engineers, setEngineers] = useState<Engineer[]>(mockEngineers);

  const addFault = (faultData: Omit<Fault, 'id' | 'reportedAt'>) => {
    const newFault: Fault = {
      ...faultData,
      id: `FAULT-${String(faults.length + 1).padStart(3, '0')}`,
      reportedAt: new Date().toISOString(),
      status: 'مفتوح',
    };
    setFaults(prevFaults => [...prevFaults, newFault]);
  };

  const updateFault = (faultId: string, updatedData: Partial<Fault>) => {
    setFaults(prevFaults =>
      prevFaults.map(fault =>
        fault.id === faultId ? { ...fault, ...updatedData } : fault
      )
    );
  };

  const addEngineer = (engineerData: Omit<Engineer, 'id' | 'joinedAt'>) => {
    const newEngineer: Engineer = {
      ...engineerData,
      id: `ENG-${String(engineers.length + 1).padStart(3, '0')}`,
      joinedAt: new Date().toISOString(),
    };
    setEngineers(prevEngineers => [...prevEngineers, newEngineer]);
  };

  const assignEngineerToFault = (faultId: string, engineerName: string) => {
    setFaults(prevFaults =>
      prevFaults.map(fault =>
        fault.id === faultId
          ? { ...fault, engineer: engineerName, status: 'قيد المعالجة' as FaultStatus }
          : fault
      )
    );

    // Update engineer status to busy
    setEngineers(prevEngineers =>
      prevEngineers.map(engineer =>
        engineer.name === engineerName
          ? { ...engineer, status: 'مشغول' as Engineer['status'] }
          : engineer
      )
    );
  };

  const getAvailableEngineers = (): Engineer[] => {
    return engineers.filter(engineer => engineer.status === 'متاح');
  };

  const updateFaultStatus = (faultId: string, status: FaultStatus) => {
    const fault = faults.find(f => f.id === faultId);
    const updatedData: Partial<Fault> = { status };
    
    if (status === 'تم الإصلاح') {
      updatedData.completedAt = new Date().toISOString();
      
      // Free up the engineer
      if (fault?.engineer) {
        setEngineers(prevEngineers =>
          prevEngineers.map(engineer =>
            engineer.name === fault.engineer
              ? { ...engineer, status: 'متاح' as Engineer['status'] }
              : engineer
          )
        );
      }
    }

    updateFault(faultId, updatedData);
  };

  const updateEngineer = (engineerId: string, updatedData: EngineerUpdateData) => {
    setEngineers(prevEngineers =>
      prevEngineers.map(engineer =>
        engineer.id === engineerId ? { ...engineer, ...updatedData } : engineer
      )
    );
  };

  const deleteFault = (faultId: string) => {
    const fault = faults.find(f => f.id === faultId);
    
    // Free up the engineer if assigned
    if (fault?.engineer) {
      setEngineers(prevEngineers =>
        prevEngineers.map(engineer =>
          engineer.name === fault.engineer
            ? { ...engineer, status: 'متاح' as Engineer['status'] }
            : engineer
        )
      );
    }
    
    setFaults(prevFaults => prevFaults.filter(fault => fault.id !== faultId));
  };

  const deleteEngineer = (engineerId: string) => {
    const engineer = engineers.find(e => e.id === engineerId);
    
    // Remove engineer assignment from faults
    if (engineer) {
      setFaults(prevFaults =>
        prevFaults.map(fault =>
          fault.engineer === engineer.name
            ? { ...fault, engineer: undefined, status: 'مفتوح' as FaultStatus }
            : fault
        )
      );
    }
    
    setEngineers(prevEngineers => prevEngineers.filter(engineer => engineer.id !== engineerId));
  };

  const addVideoToFault = (faultId: string, video: VideoAttachment) => {
    setFaults(prevFaults =>
      prevFaults.map(fault => {
        if (fault.id === faultId) {
          if (video.type === 'before') {
            return {
              ...fault,
              videosBeforeMaintenance: [...(fault.videosBeforeMaintenance || []), video]
            };
          } else {
            return {
              ...fault,
              videosAfterMaintenance: [...(fault.videosAfterMaintenance || []), video]
            };
          }
        }
        return fault;
      })
    );
  };

  const removeVideoFromFault = (faultId: string, videoId: string) => {
    setFaults(prevFaults =>
      prevFaults.map(fault => {
        if (fault.id === faultId) {
          return {
            ...fault,
            videosBeforeMaintenance: fault.videosBeforeMaintenance?.filter(v => v.id !== videoId) || [],
            videosAfterMaintenance: fault.videosAfterMaintenance?.filter(v => v.id !== videoId) || []
          };
        }
        return fault;
      })
    );
  };

  const updateFaultVideos = (faultId: string, videosBeforeMaintenance?: VideoAttachment[], videosAfterMaintenance?: VideoAttachment[]) => {
    setFaults(prevFaults =>
      prevFaults.map(fault => {
        if (fault.id === faultId) {
          return {
            ...fault,
            ...(videosBeforeMaintenance !== undefined && { videosBeforeMaintenance }),
            ...(videosAfterMaintenance !== undefined && { videosAfterMaintenance })
          };
        }
        return fault;
      })
    );
  };

  const value = { 
    faults, 
    engineers, 
    addFault, 
    updateFault, 
    addEngineer, 
    assignEngineerToFault, 
    getAvailableEngineers, 
    updateFaultStatus, 
    updateEngineer, 
    deleteFault, 
    deleteEngineer,
    addVideoToFault,
    removeVideoFromFault,
    updateFaultVideos
  };

  return <DataContext.Provider value={value}>{children}</DataContext.Provider>;
};
