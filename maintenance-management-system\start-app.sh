#!/bin/bash

# نظام إدارة الصيانة - Maintenance Management System
# Bash Startup Script

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo ""
echo -e "${CYAN}========================================${NC}"
echo -e "${YELLOW}    نظام إدارة الصيانة${NC}"
echo -e "${YELLOW}    Maintenance Management System${NC}"
echo -e "${CYAN}========================================${NC}"
echo ""

# Check Node.js
echo -e "${BLUE}[1/4] التحقق من Node.js...${NC}"
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    echo -e "${GREEN}✅ Node.js مثبت بنجاح: $NODE_VERSION${NC}"
else
    echo -e "${RED}❌ خطأ: Node.js غير مثبت!${NC}"
    echo ""
    echo -e "${YELLOW}يرجى تثبيت Node.js من الرابط التالي:${NC}"
    echo -e "${CYAN}https://nodejs.org${NC}"
    echo ""
    echo -e "${YELLOW}أو استخدم أحد الأوامر التالية:${NC}"
    echo ""
    echo -e "${CYAN}# macOS (Homebrew)${NC}"
    echo "brew install node"
    echo ""
    echo -e "${CYAN}# Ubuntu/Debian${NC}"
    echo "sudo apt update && sudo apt install nodejs npm"
    echo ""
    echo -e "${CYAN}# CentOS/RHEL${NC}"
    echo "sudo yum install nodejs npm"
    echo ""
    read -p "اضغط Enter للخروج..."
    exit 1
fi

# Check npm
echo ""
echo -e "${BLUE}[2/4] التحقق من npm...${NC}"
if command -v npm &> /dev/null; then
    NPM_VERSION=$(npm --version)
    echo -e "${GREEN}✅ npm متوفر: $NPM_VERSION${NC}"
else
    echo -e "${RED}❌ خطأ: npm غير متوفر!${NC}"
    read -p "اضغط Enter للخروج..."
    exit 1
fi

# Install dependencies
echo ""
echo -e "${BLUE}[3/4] تثبيت التبعيات...${NC}"

if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}تثبيت التبعيات للمرة الأولى...${NC}"
    echo -e "${YELLOW}هذا قد يستغرق بضع دقائق...${NC}"
    
    if npm install; then
        echo -e "${GREEN}✅ تم تثبيت التبعيات بنجاح${NC}"
    else
        echo -e "${RED}❌ خطأ في تثبيت التبعيات!${NC}"
        echo ""
        echo -e "${YELLOW}جرب الحلول التالية:${NC}"
        echo "1. تأكد من اتصال الإنترنت"
        echo "2. شغل الأمر: npm cache clean --force"
        echo "3. احذف مجلد node_modules وأعد المحاولة"
        echo "4. تأكد من الصلاحيات: sudo chown -R \$(whoami) ~/.npm"
        echo ""
        read -p "اضغط Enter للخروج..."
        exit 1
    fi
else
    echo -e "${GREEN}✅ التبعيات مثبتة مسبقاً${NC}"
fi

# Start the application
echo ""
echo -e "${BLUE}[4/4] تشغيل التطبيق...${NC}"
echo ""
echo -e "${CYAN}========================================${NC}"
echo -e "${GREEN}التطبيق جاهز! سيفتح في المتصفح تلقائياً${NC}"
echo -e "${YELLOW}الرابط: http://localhost:8080${NC}"
echo ""
echo -e "${RED}لإيقاف التطبيق: اضغط Ctrl+C${NC}"
echo -e "${CYAN}========================================${NC}"
echo ""

# Wait a moment
sleep 3

# Open browser (try different commands for different systems)
if command -v open &> /dev/null; then
    # macOS
    open http://localhost:8080
elif command -v xdg-open &> /dev/null; then
    # Linux
    xdg-open http://localhost:8080
elif command -v gnome-open &> /dev/null; then
    # Older Linux
    gnome-open http://localhost:8080
else
    echo -e "${YELLOW}افتح المتصفح يدوياً واذهب إلى: http://localhost:8080${NC}"
fi

# Start the development server
if npm run dev; then
    echo ""
    echo -e "${GREEN}تم إيقاف التطبيق بنجاح${NC}"
else
    echo ""
    echo -e "${RED}❌ خطأ في تشغيل التطبيق!${NC}"
    read -p "اضغط Enter للخروج..."
fi
