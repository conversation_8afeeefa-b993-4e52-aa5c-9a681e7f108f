import { useState } from "react";
import { Engineer } from "@/types";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
  DialogDescription,
} from "@/components/ui/dialog";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";

interface AssignEngineerDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  engineers: Engineer[];
  onAssignConfirm: (engineerName: string) => void;
}

export function AssignEngineerDialog({ open, onOpenChange, engineers, onAssignConfirm }: AssignEngineerDialogProps) {
  const [selectedEngineer, setSelectedEngineer] = useState<string | null>(null);

  const handleAssign = () => {
    if (selectedEngineer) {
      onAssignConfirm(selectedEngineer);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>تعيين مهندس</DialogTitle>
          <DialogDescription>
            اختر مهندسًا متاحًا من القائمة لتعيينه لهذا العطل.
          </DialogDescription>
        </DialogHeader>
        <ScrollArea className="h-72 w-full">
            <RadioGroup value={selectedEngineer || undefined} onValueChange={setSelectedEngineer} className="p-4">
            {engineers.length > 0 ? (
                engineers.map((engineer) => (
                <div key={engineer.id} className="flex items-center space-x-2 space-x-reverse my-2">
                    <RadioGroupItem value={engineer.name} id={engineer.id} />
                    <Label htmlFor={engineer.id} className="flex-1">
                        <div className="flex justify-between items-center">
                            <span>{engineer.name}</span>
                            <span className="text-xs text-muted-foreground">{engineer.specialty}</span>
                        </div>
                    </Label>
                </div>
                ))
            ) : (
                <p className="text-center text-muted-foreground">لا يوجد مهندسون متاحون حاليًا.</p>
            )}
            </RadioGroup>
        </ScrollArea>
        <DialogFooter className="pt-4">
          <DialogClose asChild>
            <Button type="button" variant="secondary">إلغاء</Button>
          </DialogClose>
          <Button type="button" onClick={handleAssign} disabled={!selectedEngineer}>
            تأكيد التعيين
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}