import { Table } from "@tanstack/react-table"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { EngineerSpecialty, EngineerStatus } from "@/types"
import { X } from "lucide-react"
import { DataTableFacetedFilter } from "../shared/DataTableFacetedFilter"
import { DataTableViewOptions } from "../shared/DataTableViewOptions"

interface EngineersTableToolbarProps<TData> {
  table: Table<TData>
}

const statuses: { label: string, value: EngineerStatus }[] = [
  { label: "متاح", value: "متاح" },
  { label: "مشغول", value: "مشغول" },
  { label: "في إجازة", value: "في إجازة" },
];

const specialties: { label: string, value: EngineerSpecialty }[] = [
  { label: "أجهزة ليزر", value: "أجهزة ليزر" },
  { label: "أجهزة تخسيس", value: "أجهزة تخسيس" },
  { label: "عام", value: "عام" },
];

export function EngineersTableToolbar<TData>({ table }: EngineersTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0
  const filteredRowCount = table.getFilteredRowModel().rows.length
  const totalRowCount = table.getCoreRowModel().rows.length

  return (
    <div className="flex items-center justify-between">
      <div className="flex flex-1 items-center gap-2">
        <Input
          placeholder="بحث باسم المهندس..."
          value={(table.getColumn("name")?.getFilterValue() as string) ?? ""}
          onChange={(event) =>
            table.getColumn("name")?.setFilterValue(event.target.value)
          }
          className="h-8 w-[150px] lg:w-[250px]"
        />
        {table.getColumn("status") && (
          <DataTableFacetedFilter
            column={table.getColumn("status")}
            title="الحالة"
            options={statuses}
          />
        )}
        {table.getColumn("specialty") && (
          <DataTableFacetedFilter
            column={table.getColumn("specialty")}
            title="التخصص"
            options={specialties}
          />
        )}
        {isFiltered && (
          <Button
            variant="ghost"
            onClick={() => table.resetColumnFilters()}
            className="h-8 px-2 lg:px-3"
          >
            إلغاء الفلترة
            <X className="ms-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <div className="flex items-center gap-4">
        <div className="text-sm text-muted-foreground">
          عرض {filteredRowCount} من أصل {totalRowCount} مهندس
        </div>
        <DataTableViewOptions table={table} />
      </div>
    </div>
  )
}