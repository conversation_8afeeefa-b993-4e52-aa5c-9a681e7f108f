# دليل التثبيت والتشغيل - Installation Guide

## 🚀 تشغيل سريع (Quick Start)

### الطريقة الأولى: تشغيل مباشر
```bash
# 1. فك الضغط
unzip maintenance-management-system.zip
cd maintenance-management-system

# 2. تثبيت التبعيات
npm install

# 3. تشغيل التطبيق
npm run dev

# 4. فتح المتصفح على
http://localhost:8080
```

### الطريقة الثانية: استخدام ملفات التشغيل (Windows)
1. فك ضغط الملف `maintenance-management-system.zip`
2. ادخل إلى مجلد `maintenance-management-system`
3. انقر نقرة مزدوجة على `start-app.bat`
4. انتظر حتى يتم تثبيت التبعيات وتشغيل التطبيق
5. سيفتح المتصفح تلقائياً على `http://localhost:8080`

## 📋 المتطلبات (Requirements)

### متطلبات النظام
- **نظام التشغيل**: Windows 10+, macOS 10.15+, Linux Ubuntu 18+
- **الذاكرة**: 4GB RAM كحد أدنى (8GB مُوصى به)
- **مساحة القرص**: 500MB مساحة فارغة
- **الإنترنت**: مطلوب للتثبيت الأولي فقط

### متطلبات البرمجيات
- **Node.js**: الإصدار 18.0.0 أو أحدث
- **npm**: يأتي مع Node.js (الإصدار 8.0.0 أو أحدث)
- **متصفح حديث**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+

## 🔧 تثبيت Node.js

### Windows
1. اذهب إلى [nodejs.org](https://nodejs.org)
2. حمل النسخة LTS (الموصى بها)
3. شغل الملف المحمل واتبع التعليمات
4. أعد تشغيل الكمبيوتر

### macOS
```bash
# باستخدام Homebrew
brew install node

# أو حمل من الموقع الرسمي
# https://nodejs.org
```

### Linux (Ubuntu/Debian)
```bash
# تحديث النظام
sudo apt update

# تثبيت Node.js
sudo apt install nodejs npm

# التحقق من الإصدار
node --version
npm --version
```

## 📦 خطوات التثبيت التفصيلية

### 1. فك الضغط
```bash
# Windows (PowerShell)
Expand-Archive -Path "maintenance-management-system.zip" -DestinationPath "."

# macOS/Linux
unzip maintenance-management-system.zip
```

### 2. الدخول إلى المجلد
```bash
cd maintenance-management-system
```

### 3. التحقق من Node.js
```bash
node --version  # يجب أن يظهر v18.0.0 أو أحدث
npm --version   # يجب أن يظهر 8.0.0 أو أحدث
```

### 4. تثبيت التبعيات
```bash
npm install
```

### 5. تشغيل التطبيق
```bash
npm run dev
```

### 6. فتح التطبيق
افتح المتصفح واذهب إلى: `http://localhost:8080`

## 🌐 الوصول للتطبيق

### محلياً (Local)
- **الرابط**: http://localhost:8080
- **المنفذ**: 8080 (افتراضي)

### من أجهزة أخرى في الشبكة
- **الرابط**: http://[IP-ADDRESS]:8080
- **مثال**: http://*************:8080

لمعرفة عنوان IP الخاص بك:
```bash
# Windows
ipconfig

# macOS/Linux
ifconfig
```

## 🔧 حل المشاكل الشائعة

### مشكلة: "node is not recognized"
**الحل**: تأكد من تثبيت Node.js وإعادة تشغيل terminal/cmd

### مشكلة: "npm install fails"
**الحل**: 
```bash
# مسح cache
npm cache clean --force

# حذف node_modules وإعادة التثبيت
rm -rf node_modules package-lock.json
npm install
```

### مشكلة: "Port 8080 is already in use"
**الحل**: 
```bash
# تغيير المنفذ
npm run dev -- --port 3000
```

### مشكلة: "Permission denied" (Linux/macOS)
**الحل**:
```bash
# تثبيت npm packages عالمياً بدون sudo
npm config set prefix ~/.npm-global
export PATH=~/.npm-global/bin:$PATH
```

### مشكلة: التطبيق لا يفتح في المتصفح
**الحل**:
1. تأكد من أن التطبيق يعمل (يجب أن ترى "ready in XXXms")
2. افتح المتصفح يدوياً واذهب إلى http://localhost:8080
3. تأكد من عدم وجود firewall يحجب المنفذ

## 📱 اختبار التطبيق

### التحقق من عمل التطبيق
1. **لوحة التحكم**: يجب أن تظهر إحصائيات
2. **الأعطال**: يجب أن تظهر قائمة بالأعطال التجريبية
3. **المهندسون**: يجب أن تظهر قائمة بالمهندسين
4. **التقارير**: يجب أن تظهر إحصائيات مفصلة

### إضافة عطل تجريبي
1. اذهب إلى صفحة "الأعطال"
2. انقر "إضافة عطل جديد"
3. املأ النموذج واحفظ
4. تأكد من ظهور العطل في القائمة

## 🔄 تحديث التطبيق

### تحديث التبعيات
```bash
npm update
```

### إعادة تشغيل التطبيق
```bash
# إيقاف التطبيق (Ctrl+C)
# ثم إعادة التشغيل
npm run dev
```

## 🏗️ بناء التطبيق للإنتاج

```bash
# بناء التطبيق
npm run build

# معاينة البناء
npm run preview
```

## 📞 الدعم الفني

### في حالة وجود مشاكل:
1. تأكد من تثبيت Node.js الإصدار الصحيح
2. تأكد من اتصال الإنترنت أثناء التثبيت
3. جرب حذف `node_modules` وإعادة `npm install`
4. تأكد من عدم وجود antivirus يحجب الملفات

### معلومات مفيدة للدعم:
```bash
# معلومات النظام
node --version
npm --version
npm list --depth=0
```

---

**ملاحظة**: هذا التطبيق مصمم للعمل محلياً ولا يحتاج اتصال إنترنت بعد التثبيت.
