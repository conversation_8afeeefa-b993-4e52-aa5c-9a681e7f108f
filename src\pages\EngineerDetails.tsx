import { useParams } from "react-router-dom";
import { useData } from "../context/DataContext";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Phone, Mail, MapPin, Wrench } from "lucide-react";
import { FaultsDataTable } from "@/components/faults/FaultsDataTable";
import { columns as faultColumns } from "@/components/faults/columns";
import { EngineerStatus } from "@/types";

const EngineerDetails = () => {
  const { engineerId } = useParams<{ engineerId: string }>();
  const { engineers, faults } = useData();

  const engineer = engineers.find((e) => e.id === engineerId);
  const assignedFaults = engineer ? faults.filter(f => f.engineer === engineer.name) : [];

  if (!engineer) {
    return (
      <div className="text-center">
        <h1 className="text-2xl font-bold">المهندس غير موجود</h1>
        <p className="text-muted-foreground">لم نتمكن من العثور على بيانات المهندس المطلوب.</p>
      </div>
    );
  }

  const statusVariant: Record<EngineerStatus, "default" | "secondary" | "outline"> = {
    "متاح": "default",
    "مشغول": "secondary",
    "في إجازة": "outline",
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between flex-wrap gap-4">
        <h1 className="text-3xl font-bold">{engineer.name}</h1>
        <Badge variant={statusVariant[engineer.status]}>{engineer.status}</Badge>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-1">
          <CardHeader><CardTitle>معلومات الاتصال</CardTitle></CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center"><Phone className="me-3 text-muted-foreground" size={20} /><span>{engineer.phone}</span></div>
            <div className="flex items-center"><Mail className="me-3 text-muted-foreground" size={20} /><span>{engineer.email}</span></div>
          </CardContent>
        </Card>
        
        <Card className="lg:col-span-2">
          <CardHeader><CardTitle>التخصص والمنطقة</CardTitle></CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center"><Wrench className="me-3 text-muted-foreground" size={20} /><span className="font-semibold">{engineer.specialty}</span></div>
            <div className="flex items-center"><MapPin className="me-3 text-muted-foreground" size={20} /><span>{engineer.area}</span></div>
          </CardContent>
        </Card>
      </div>

      <div>
        <h2 className="text-2xl font-bold mb-4">الأعطال المسندة</h2>
        <FaultsDataTable columns={faultColumns} data={assignedFaults} />
      </div>
    </div>
  );
};

export default EngineerDetails;