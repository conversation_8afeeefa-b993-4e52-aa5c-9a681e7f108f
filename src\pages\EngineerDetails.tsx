import { useParams } from "react-router-dom";
import { useData } from "../context/DataContext";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Phone, Mail, MapPin, Wrench, CheckCircle, Clock, AlertTriangle } from "lucide-react";
import { FaultsDataTable } from "@/components/faults/FaultsDataTable";
import { columns as faultColumns } from "@/components/faults/columns";
import { EngineerStatus } from "@/types";

const EngineerDetails = () => {
  const { engineerId } = useParams<{ engineerId: string }>();
  const { engineers, faults } = useData();

  const engineer = engineers.find((e) => e.id === engineerId);
  const assignedFaults = engineer ? faults.filter(f => f.engineer === engineer.name) : [];

  // إحصائيات مفصلة
  const completedFaults = assignedFaults.filter(f => f.status === 'تم الإصلاح');
  const activeFaults = assignedFaults.filter(f => f.status === 'مفتوح' || f.status === 'قيد المعالجة');
  const urgentFaults = assignedFaults.filter(f => f.priority === 'عاجل' && (f.status === 'مفتوح' || f.status === 'قيد المعالجة'));

  if (!engineer) {
    return (
      <div className="text-center">
        <h1 className="text-2xl font-bold">المهندس غير موجود</h1>
        <p className="text-muted-foreground">لم نتمكن من العثور على بيانات المهندس المطلوب.</p>
      </div>
    );
  }

  const statusVariant: Record<EngineerStatus, "default" | "secondary" | "outline"> = {
    "متاح": "default",
    "مشغول": "secondary",
    "في إجازة": "outline",
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between flex-wrap gap-4">
        <h1 className="text-3xl font-bold">{engineer.name}</h1>
        <Badge variant={statusVariant[engineer.status]}>{engineer.status}</Badge>
      </div>

      {/* إحصائيات الأداء */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card className="border-0 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-700 dark:text-green-300">
              أعطال مكتملة
            </CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{completedFaults.length}</div>
            <p className="text-xs text-green-600/70">تم إصلاحها بنجاح</p>
          </CardContent>
        </Card>

        <Card className="border-0 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-700 dark:text-blue-300">
              أعطال نشطة
            </CardTitle>
            <Clock className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{activeFaults.length}</div>
            <p className="text-xs text-blue-600/70">قيد العمل عليها</p>
          </CardContent>
        </Card>

        <Card className="border-0 bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-950/20 dark:to-pink-950/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-red-700 dark:text-red-300">
              أعطال عاجلة
            </CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{urgentFaults.length}</div>
            <p className="text-xs text-red-600/70">تحتاج اهتمام فوري</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-1">
          <CardHeader><CardTitle>معلومات الاتصال</CardTitle></CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center"><Phone className="me-3 text-muted-foreground" size={20} /><span>{engineer.phone}</span></div>
            <div className="flex items-center"><Mail className="me-3 text-muted-foreground" size={20} /><span>{engineer.email}</span></div>
          </CardContent>
        </Card>

        <Card className="lg:col-span-2">
          <CardHeader><CardTitle>التخصص والمنطقة</CardTitle></CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center"><Wrench className="me-3 text-muted-foreground" size={20} /><span className="font-semibold">{engineer.specialty}</span></div>
            <div className="flex items-center"><MapPin className="me-3 text-muted-foreground" size={20} /><span>{engineer.area}</span></div>
          </CardContent>
        </Card>
      </div>

      <div>
        <h2 className="text-2xl font-bold mb-4">الأعطال المسندة</h2>
        <FaultsDataTable columns={faultColumns} data={assignedFaults} />
      </div>
    </div>
  );
};

export default EngineerDetails;