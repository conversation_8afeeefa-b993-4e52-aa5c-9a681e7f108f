import { Building, Sparkles } from "lucide-react";
import { SidebarNav } from "./SidebarNav";

const Sidebar = () => {
  return (
    <div className="hidden lg:flex lg:flex-col lg:w-64 sidebar-modern border-l border-border/50 relative">
      {/* تأثير الزجاج */}
      <div className="absolute inset-0 bg-gradient-to-b from-card/80 to-card/60 backdrop-blur-xl" />

      {/* المحتوى */}
      <div className="relative z-10 flex flex-col h-full">
        {/* الهيدر */}
        <div className="flex items-center justify-center h-16 border-b border-border/30 relative">
          <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-accent/10" />
          <div className="relative flex items-center">
            <div className="relative">
              <Building className="h-7 w-7 text-primary" />
              <Sparkles className="h-3 w-3 text-accent absolute -top-1 -right-1" />
            </div>
            <div className="ms-3">
              <h1 className="text-xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                أواسيس فارما
              </h1>
              <p className="text-xs text-muted-foreground">نظام إدارة الصيانة</p>
            </div>
          </div>
        </div>

        {/* التنقل */}
        <div className="flex-1">
          <SidebarNav />
        </div>

        {/* الفوتر */}
        <div className="p-4 border-t border-border/30">
          <div className="text-xs text-muted-foreground text-center">
            <p>الإصدار 2.0</p>
            <p className="mt-1">تصميم حديث ومتطور</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;