# كيفية تشغيل نظام إدارة الأعطال

## طرق التشغيل السريع:

### الطريقة الأولى (الأسهل):
1. انقر نقراً مزدوجاً على ملف `START_HERE.bat`
2. اتبع التعليمات التي تظهر على الشاشة
3. سيفتح التطبيق في المتصفح على العنوان: http://localhost:8080

### الطريقة الثانية:
1. انقر نقراً مزدوجاً على ملف `run.cmd`
2. انتظر حتى يبدأ الخادم
3. افتح المتصفح واذهب إلى: http://localhost:8080

### الطريقة الثالثة (من سطر الأوامر):
1. افتح Command Prompt أو PowerShell
2. انتقل إلى مجلد المشروع
3. شغل أحد الأوامر التالية:
   ```
   npm run dev
   ```
   أو
   ```
   npx vite
   ```
   أو
   ```
   node node_modules/vite/bin/vite.js
   ```

## متطلبات التشغيل:
- Node.js (الإصدار 16 أو أحدث)
- npm (يأتي مع Node.js)

## في حالة وجود مشاكل:
1. تأكد من تثبيت Node.js من: https://nodejs.org/
2. شغل الأمر `npm install` في مجلد المشروع
3. حاول مرة أخرى

## الميزات الجديدة المضافة:
✅ إدخال نوع الجهاز يدوياً (بدلاً من القائمة المحددة)
✅ طباعة وحفظ التقارير كـ PDF
✅ طباعة تقرير دورة الصيانة الكاملة للعميل من صفحة تفاصيل العطل
✅ تصميم حديث وجذاب بألوان متدرجة
✅ رفع ملفات CSV وإنشاء تقارير عليها
✅ تحسينات شاملة لقسم المهندسين
✅ إحصائيات مفصلة للمهندسين في لوحة التحكم
✅ عرض الأعطال المسندة لكل مهندس
✅ تحسين واجهة إضافة المهندسين
✅ رفع وإدارة فيديوهات الصيانة (قبل وبعد)

## تفاصيل الميزات الجديدة:

### 1. إدخال نوع الجهاز يدوياً:
- في صفحة إضافة عطل جديد، يمكن الآن كتابة نوع الجهاز بحرية
- لا توجد قيود على أنواع الأجهزة المدعومة

### 2. طباعة التقارير العامة:
- في صفحة التقارير، يمكن طباعة تقرير شامل لجميع الأعطال المنجزة
- يتضمن إحصائيات ومخططات وجداول مفصلة

### 3. تقرير دورة الصيانة الكاملة:
- في صفحة تفاصيل أي عطل، يمكن طباعة تقرير شامل للعميل
- يتضمن جميع أعطال العميل منذ بداية التعامل
- إحصائيات مفصلة عن أداء الصيانة للعميل
- تمييز العطل الحالي في التقرير

### 4. التصميم الحديث:
- ألوان متدرجة جذابة (أزرق/بنفسجي، أخضر، برتقالي)
- تأثيرات بصرية حديثة (ظلال، شفافية، حركات ناعمة)
- أيقونات ملونة في الشريط الجانبي
- كروت حديثة مع خلفيات متدرجة

### 5. رفع ملفات CSV:
- في صفحة التقارير، تبويب "تقارير CSV"
- رفع ملفات CSV وإنشاء تقارير مفصلة عليها
- معاينة البيانات قبل إنشاء التقرير
- طباعة تقارير احترافية من البيانات المرفوعة

### 6. تحسينات قسم المهندسين:
- **لوحة التحكم المحسنة**: إحصائيات مفصلة للمهندسين (متاح، مشغول، في إجازة)
- **توزيع التخصصات**: عرض عدد المهندسين لكل تخصص (ليزر، تخسيس، عام)
- **صفحة المهندسين المحسنة**:
  - كروت إحصائية ملونة لحالات المهندسين
  - عرض الأعطال المسندة وغير المسندة
  - زر إضافة مهندس محسن بتصميم متدرج
- **جدول المهندسين المحسن**:
  - عرض عدد الأعطال المسندة لكل مهندس
  - أيقونات ملونة لحالات المهندسين
  - عداد النتائج في شريط الأدوات
- **صفحة تفاصيل المهندس**:
  - إحصائيات الأداء (أعطال مكتملة، نشطة، عاجلة)
  - كروت ملونة لعرض الإحصائيات
  - تصميم محسن ومنظم

### 7. إدارة فيديوهات الصيانة:
- **رفع فيديوهات قبل الصيانة**:
  - في نموذج إضافة عطل جديد
  - في صفحة تفاصيل العطل (تبويب رفع فيديوهات)
  - توثيق الحالة الأولية للجهاز
- **رفع فيديوهات بعد الصيانة**:
  - متاح فقط عند إكمال الإصلاح
  - إثبات نجاح عملية الصيانة
  - توثيق الحالة النهائية للجهاز
- **عرض وإدارة الفيديوهات**:
  - مشغل فيديو مدمج في نافذة منبثقة
  - تحميل الفيديوهات
  - فتح في نافذة جديدة
  - حذف الفيديوهات (للمحررين)
- **الميزات التقنية**:
  - دعم جميع صيغ الفيديو الشائعة (MP4, AVI, MOV, WMV)
  - حد أقصى 100MB لكل فيديو
  - رفع متعدد للفيديوهات
  - معاينة فورية للفيديوهات المرفوعة
- **التحكم في الصلاحيات**:
  - رفع فيديوهات قبل الصيانة: متاح دائماً
  - رفع فيديوهات بعد الصيانة: فقط للأعطال المكتملة أو قيد المعالجة
  - حذف الفيديوهات: فقط للمحررين

## عناوين التطبيق:
- الصفحة الرئيسية: http://localhost:8083
- لوحة التحكم: http://localhost:8083/
- إدارة الأعطال: http://localhost:8083/faults
- إدارة المهندسين: http://localhost:8083/engineers
- التقارير: http://localhost:8083/reports
