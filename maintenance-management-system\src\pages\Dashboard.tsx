import { Link } from "react-router-dom";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Wrench, Users, FileText, AlertTriangle, Video } from "lucide-react";
import { useData } from "@/context/DataContext";

const Dashboard = () => {
  const { faults, engineers } = useData();

  // إحصائيات الأعطال
  const totalFaults = faults.length;
  const openFaults = faults.filter(f => f.status === 'مفتوح').length;
  const inProgressFaults = faults.filter(f => f.status === 'قيد المعالجة').length;
  const completedFaults = faults.filter(f => f.status === 'تم الإصلاح').length;
  const urgentFaults = faults.filter(f => f.priority === 'عاجل' && f.status !== 'تم الإصلاح').length;

  // إحصائيات المهندسين
  const availableEngineers = engineers.filter(e => e.status === 'متاح').length;
  const busyEngineers = engineers.filter(e => e.status === 'مشغول').length;
  const onLeaveEngineers = engineers.filter(e => e.status === 'في إجازة').length;
  const totalEngineers = engineers.length;

  // إحصائيات التخصصات
  const laserEngineers = engineers.filter(e => e.specialty === 'أجهزة ليزر').length;
  const slimmingEngineers = engineers.filter(e => e.specialty === 'أجهزة تخسيس').length;
  const generalEngineers = engineers.filter(e => e.specialty === 'عام').length;

  // إحصائيات الفيديوهات
  const faultsWithBeforeVideos = faults.filter(f => f.videosBeforeMaintenance && f.videosBeforeMaintenance.length > 0).length;
  const faultsWithAfterVideos = faults.filter(f => f.videosAfterMaintenance && f.videosAfterMaintenance.length > 0).length;
  const totalVideos = faults.reduce((acc, fault) => {
    return acc + (fault.videosBeforeMaintenance?.length || 0) + (fault.videosAfterMaintenance?.length || 0);
  }, 0);

  const reportsLink = "/reports";

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">لوحة التحكم</h1>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          نظرة عامة على حالة النظام والإحصائيات
        </p>
      </div>

      {/* الكروت الإحصائية */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Link to="/faults" className="group">
          <Card className="modern-card hover:scale-105 transition-all duration-300 border-0 relative overflow-hidden">
            {/* خلفية متدرجة */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-indigo-500/10" />

            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
              <CardTitle className="text-sm font-medium text-foreground/80">
                إجمالي الأعطال
              </CardTitle>
              <div className="p-2 rounded-lg bg-blue-500/20">
                <Wrench className="h-5 w-5 text-blue-600" />
              </div>
            </CardHeader>
            <CardContent className="relative z-10">
              <div className="text-3xl font-bold text-blue-600">{totalFaults}</div>
              <p className="text-xs text-muted-foreground mt-1">
                {openFaults} مفتوح، {inProgressFaults} قيد المعالجة
              </p>
              <div className="mt-3 h-1 bg-blue-200 rounded-full overflow-hidden">
                <div
                  className="h-full bg-gradient-to-r from-blue-400 to-indigo-600 rounded-full transition-all duration-500"
                  style={{ width: `${totalFaults > 0 ? (completedFaults / totalFaults) * 100 : 0}%` }}
                />
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link to="/engineers" className="group">
          <Card className="modern-card hover:scale-105 transition-all duration-300 border-0 relative overflow-hidden">
            {/* خلفية متدرجة */}
            <div className="absolute inset-0 bg-gradient-to-br from-green-500/10 to-emerald-500/10" />

            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
              <CardTitle className="text-sm font-medium text-foreground/80">
                مهندسون متاحون
              </CardTitle>
              <div className="p-2 rounded-lg bg-green-500/20">
                <Users className="h-5 w-5 text-green-600" />
              </div>
            </CardHeader>
            <CardContent className="relative z-10">
              <div className="text-3xl font-bold text-green-600">{availableEngineers}</div>
              <p className="text-xs text-muted-foreground mt-1">
                من أصل {totalEngineers} مهندس
              </p>
              <div className="mt-3 h-1 bg-green-200 rounded-full overflow-hidden">
                <div
                  className="h-full bg-gradient-to-r from-green-400 to-emerald-600 rounded-full transition-all duration-500"
                  style={{ width: `${totalEngineers > 0 ? (availableEngineers / totalEngineers) * 100 : 0}%` }}
                />
              </div>
            </CardContent>
          </Card>
        </Link>
        
        <Link to="/engineers?status=مشغول" className="group">
          <Card className="modern-card hover:scale-105 transition-all duration-300 border-0 relative overflow-hidden">
            {/* خلفية متدرجة */}
            <div className="absolute inset-0 bg-gradient-to-br from-yellow-500/10 to-orange-500/10" />

            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
              <CardTitle className="text-sm font-medium text-foreground/80">
                مهندسون مشغولون
              </CardTitle>
              <div className="p-2 rounded-lg bg-yellow-500/20">
                <Users className="h-5 w-5 text-yellow-600" />
              </div>
            </CardHeader>
            <CardContent className="relative z-10">
              <div className="text-3xl font-bold text-yellow-600">{busyEngineers}</div>
              <p className="text-xs text-muted-foreground mt-1">
                يعملون حالياً على أعطال
              </p>
              <div className="mt-3 h-1 bg-yellow-200 rounded-full overflow-hidden">
                <div
                  className="h-full bg-gradient-to-r from-yellow-400 to-orange-600 rounded-full transition-all duration-500"
                  style={{ width: `${totalEngineers > 0 ? (busyEngineers / totalEngineers) * 100 : 0}%` }}
                />
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link to={reportsLink} className="group">
          <Card className="modern-card hover:scale-105 transition-all duration-300 border-0 relative overflow-hidden">
            {/* خلفية متدرجة */}
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10" />

            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
              <CardTitle className="text-sm font-medium text-foreground/80">
                التقارير
              </CardTitle>
              <div className="p-2 rounded-lg bg-purple-500/20">
                <FileText className="h-5 w-5 text-purple-600" />
              </div>
            </CardHeader>
            <CardContent className="relative z-10">
              <div className="text-3xl font-bold text-purple-600">{completedFaults}</div>
              <p className="text-xs text-muted-foreground mt-1">
                عطل مكتمل
              </p>
              <div className="mt-3 h-1 bg-purple-200 rounded-full overflow-hidden">
                <div
                  className="h-full bg-gradient-to-r from-purple-400 to-pink-600 rounded-full transition-all duration-500"
                  style={{ width: `${totalFaults > 0 ? (completedFaults / totalFaults) * 100 : 0}%` }}
                />
              </div>
            </CardContent>
          </Card>
        </Link>
      </div>

      {/* إحصائيات تخصصات المهندسين */}
      <div className="space-y-4">
        <h2 className="text-2xl font-bold">توزيع المهندسين حسب التخصص</h2>
        <div className="grid gap-4 md:grid-cols-3">
          <Card className="modern-card border-0 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10" />
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
              <CardTitle className="text-sm font-medium text-foreground/80">أجهزة ليزر</CardTitle>
              <div className="p-2 rounded-lg bg-purple-500/20">
                <Users className="h-4 w-4 text-purple-600" />
              </div>
            </CardHeader>
            <CardContent className="relative z-10">
              <div className="text-2xl font-bold text-purple-600">{laserEngineers}</div>
              <p className="text-xs text-muted-foreground">مهندس متخصص</p>
            </CardContent>
          </Card>

          <Card className="modern-card border-0 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-teal-500/10 to-cyan-500/10" />
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
              <CardTitle className="text-sm font-medium text-foreground/80">أجهزة تخسيس</CardTitle>
              <div className="p-2 rounded-lg bg-teal-500/20">
                <Users className="h-4 w-4 text-teal-600" />
              </div>
            </CardHeader>
            <CardContent className="relative z-10">
              <div className="text-2xl font-bold text-teal-600">{slimmingEngineers}</div>
              <p className="text-xs text-muted-foreground">مهندس متخصص</p>
            </CardContent>
          </Card>

          <Card className="modern-card border-0 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/10 to-blue-500/10" />
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
              <CardTitle className="text-sm font-medium text-foreground/80">عام</CardTitle>
              <div className="p-2 rounded-lg bg-indigo-500/20">
                <Users className="h-4 w-4 text-indigo-600" />
              </div>
            </CardHeader>
            <CardContent className="relative z-10">
              <div className="text-2xl font-bold text-indigo-600">{generalEngineers}</div>
              <p className="text-xs text-muted-foreground">مهندس عام</p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* إحصائيات الفيديوهات */}
      <div className="space-y-4">
        <h2 className="text-2xl font-bold">إحصائيات فيديوهات الصيانة</h2>
        <div className="grid gap-4 md:grid-cols-3">
          <Card className="modern-card border-0 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-cyan-500/10" />
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
              <CardTitle className="text-sm font-medium text-foreground/80">فيديوهات قبل الصيانة</CardTitle>
              <div className="p-2 rounded-lg bg-blue-500/20">
                <Video className="h-4 w-4 text-blue-600" />
              </div>
            </CardHeader>
            <CardContent className="relative z-10">
              <div className="text-2xl font-bold text-blue-600">{faultsWithBeforeVideos}</div>
              <p className="text-xs text-muted-foreground">عطل موثق بالفيديو</p>
            </CardContent>
          </Card>

          <Card className="modern-card border-0 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-green-500/10 to-emerald-500/10" />
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
              <CardTitle className="text-sm font-medium text-foreground/80">فيديوهات بعد الصيانة</CardTitle>
              <div className="p-2 rounded-lg bg-green-500/20">
                <Video className="h-4 w-4 text-green-600" />
              </div>
            </CardHeader>
            <CardContent className="relative z-10">
              <div className="text-2xl font-bold text-green-600">{faultsWithAfterVideos}</div>
              <p className="text-xs text-muted-foreground">إصلاح موثق بالفيديو</p>
            </CardContent>
          </Card>

          <Card className="modern-card border-0 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10" />
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
              <CardTitle className="text-sm font-medium text-foreground/80">إجمالي الفيديوهات</CardTitle>
              <div className="p-2 rounded-lg bg-purple-500/20">
                <Video className="h-4 w-4 text-purple-600" />
              </div>
            </CardHeader>
            <CardContent className="relative z-10">
              <div className="text-2xl font-bold text-purple-600">{totalVideos}</div>
              <p className="text-xs text-muted-foreground">فيديو في النظام</p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* الأعطال العاجلة */}
      {urgentFaults > 0 && (
        <div className="space-y-4">
          <h2 className="text-2xl font-bold text-red-600 flex items-center gap-2">
            <AlertTriangle className="h-6 w-6" />
            أعطال عاجلة تحتاج انتباه
          </h2>
          <Card className="border-red-200 bg-red-50 dark:bg-red-950/20">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-lg font-semibold text-red-800 dark:text-red-200">
                    {urgentFaults} عطل عاجل
                  </p>
                  <p className="text-sm text-red-600 dark:text-red-400">
                    يحتاج معالجة فورية
                  </p>
                </div>
                <Link to="/faults?priority=عاجل">
                  <Button variant="destructive">
                    عرض الأعطال العاجلة
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default Dashboard;
