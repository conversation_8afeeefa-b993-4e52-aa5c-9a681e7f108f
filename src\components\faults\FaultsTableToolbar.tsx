import { Table } from "@tanstack/react-table"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { FaultPriority, FaultStatus } from "@/types"
import { X } from "lucide-react"
import { DataTableFacetedFilter } from "../shared/DataTableFacetedFilter"
import { DataTableViewOptions } from "../shared/DataTableViewOptions"

interface FaultsTableToolbarProps<TData> {
  table: Table<TData>
}

const statuses: { label: string, value: FaultStatus }[] = [
  { label: "مفتوح", value: "مفتوح" },
  { label: "قيد المعالجة", value: "قيد المعالجة" },
  { label: "تم الإصلاح", value: "تم الإصلاح" },
  { label: "لم يتم الإصلاح", value: "لم يتم الإصلاح" },
  { label: "معلق", value: "معلق" },
];

const priorities: { label: string, value: FaultPriority }[] = [
  { label: "عاجل", value: "عاجل" },
  { label: "متوسط", value: "متوسط" },
  { label: "عادي", value: "عادي" },
];

export function FaultsTableToolbar<TData>({ table }: FaultsTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0

  return (
    <div className="flex items-center justify-between">
      <div className="flex flex-1 items-center gap-2">
        <Input
          placeholder="بحث باسم العميل..."
          value={(table.getColumn("customerName")?.getFilterValue() as string) ?? ""}
          onChange={(event) =>
            table.getColumn("customerName")?.setFilterValue(event.target.value)
          }
          className="h-8 w-[150px] lg:w-[250px]"
        />
        {table.getColumn("status") && (
          <DataTableFacetedFilter
            column={table.getColumn("status")}
            title="الحالة"
            options={statuses}
          />
        )}
        {table.getColumn("priority") && (
          <DataTableFacetedFilter
            column={table.getColumn("priority")}
            title="الأولوية"
            options={priorities}
          />
        )}
        {isFiltered && (
          <Button
            variant="ghost"
            onClick={() => table.resetColumnFilters()}
            className="h-8 px-2 lg:px-3"
          >
            إلغاء الفلترة
            <X className="ms-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <DataTableViewOptions table={table} />
    </div>
  )
}