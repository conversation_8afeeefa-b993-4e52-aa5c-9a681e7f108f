# نظام إدارة الصيانة - Maintenance Management System
# PowerShell Startup Script

# Set console encoding to UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    نظام إدارة الصيانة" -ForegroundColor Yellow
Write-Host "    Maintenance Management System" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check Node.js
Write-Host "[1/4] التحقق من Node.js..." -ForegroundColor Blue
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js مثبت بنجاح: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ خطأ: Node.js غير مثبت!" -ForegroundColor Red
    Write-Host ""
    Write-Host "يرجى تثبيت Node.js من الرابط التالي:" -ForegroundColor Yellow
    Write-Host "https://nodejs.org" -ForegroundColor Cyan
    Write-Host ""
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# Check npm
Write-Host ""
Write-Host "[2/4] التحقق من npm..." -ForegroundColor Blue
try {
    $npmVersion = npm --version
    Write-Host "✅ npm متوفر: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ خطأ: npm غير متوفر!" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# Install dependencies
Write-Host ""
Write-Host "[3/4] تثبيت التبعيات..." -ForegroundColor Blue

if (!(Test-Path "node_modules")) {
    Write-Host "تثبيت التبعيات للمرة الأولى..." -ForegroundColor Yellow
    Write-Host "هذا قد يستغرق بضع دقائق..." -ForegroundColor Yellow
    
    try {
        npm install
        Write-Host "✅ تم تثبيت التبعيات بنجاح" -ForegroundColor Green
    } catch {
        Write-Host "❌ خطأ في تثبيت التبعيات!" -ForegroundColor Red
        Write-Host ""
        Write-Host "جرب الحلول التالية:" -ForegroundColor Yellow
        Write-Host "1. تأكد من اتصال الإنترنت" -ForegroundColor White
        Write-Host "2. شغل الأمر: npm cache clean --force" -ForegroundColor White
        Write-Host "3. احذف مجلد node_modules وأعد المحاولة" -ForegroundColor White
        Write-Host ""
        Read-Host "اضغط Enter للخروج"
        exit 1
    }
} else {
    Write-Host "✅ التبعيات مثبتة مسبقاً" -ForegroundColor Green
}

# Start the application
Write-Host ""
Write-Host "[4/4] تشغيل التطبيق..." -ForegroundColor Blue
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "التطبيق جاهز! سيفتح في المتصفح تلقائياً" -ForegroundColor Green
Write-Host "الرابط: http://localhost:8080" -ForegroundColor Yellow
Write-Host ""
Write-Host "لإيقاف التطبيق: اضغط Ctrl+C" -ForegroundColor Red
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Wait a moment then open browser
Start-Sleep -Seconds 3

# Open browser
Start-Process "http://localhost:8080"

# Start the development server
try {
    npm run dev
} catch {
    Write-Host ""
    Write-Host "❌ خطأ في تشغيل التطبيق!" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
}
