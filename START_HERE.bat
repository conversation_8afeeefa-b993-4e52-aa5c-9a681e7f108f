@echo off
echo ========================================
echo    Fault Management System Launcher
echo ========================================
echo.
echo Double-click this file to start the application
echo.
echo The application will open in your web browser at:
echo http://localhost:8080
echo.
echo Press any key to start...
pause >nul

echo Starting the development server...
cd /d "%~dp0"

REM Try multiple methods to start the server
echo Trying method 1: npm run dev
call npm run dev 2>nul
if %errorlevel% equ 0 goto :success

echo Trying method 2: npx vite
call npx vite 2>nul
if %errorlevel% equ 0 goto :success

echo Trying method 3: direct vite
call node node_modules/vite/bin/vite.js 2>nul
if %errorlevel% equ 0 goto :success

echo.
echo ERROR: Could not start the development server
echo Please make sure Node.js is installed and run 'npm install' first
echo.
pause
goto :end

:success
echo.
echo Server started successfully!
echo Open your browser and go to: http://localhost:8080
echo.
echo Press Ctrl+C to stop the server
pause

:end
