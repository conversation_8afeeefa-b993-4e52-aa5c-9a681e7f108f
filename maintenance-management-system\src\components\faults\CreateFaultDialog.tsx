import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { PlusCircle, Paperclip, X, User, Video } from "lucide-react";
import { DeviceType, FaultPriority, Attachment, VideoAttachment } from "@/types";
import { showSuccess } from "@/utils/toast";
import { useData } from "../../context/DataContext";
import { Label } from "../ui/label";
import { Input } from "../ui/input";
import { Button } from "../ui/button";
// import VideoUpload from "./VideoUpload";

const formSchema = z.object({
  customerName: z.string().min(2, "اسم العميل مطلوب"),
  customerPhone: z.string().min(10, "رقم الهاتف مطلوب"),
  customerEmail: z.string().email("البريد الإلكتروني غير صحيح").optional().or(z.literal("")),
  facilityAddress: z.string().min(5, "عنوان المنشأة مطلوب"),
  deviceType: z.string().min(2, "نوع الجهاز مطلوب"),
  serialNumber: z.string().optional(),
  faultDescription: z.string().min(10, "وصف العطل مطلوب"),
  priority: z.enum(["عاجل", "متوسط", "عادي"]),
});

type FormData = z.infer<typeof formSchema>;

interface CreateFaultDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const CreateFaultDialog = ({ open, onOpenChange }: CreateFaultDialogProps) => {
  const { addFault } = useData();
  const [attachments, setAttachments] = useState<Attachment[]>([]);
  const [videosBeforeMaintenance, setVideosBeforeMaintenance] = useState<VideoAttachment[]>([]);

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      customerName: "",
      customerPhone: "",
      customerEmail: "",
      facilityAddress: "",
      deviceType: "",
      serialNumber: "",
      faultDescription: "",
      priority: "متوسط",
    },
  });

  const onSubmit = (data: FormData) => {
    try {
      addFault({
        ...data,
        attachments,
        videosBeforeMaintenance,
        status: "مفتوح",
      });
      
      showSuccess("تم إضافة العطل بنجاح");
      form.reset();
      setAttachments([]);
      setVideosBeforeMaintenance([]);
      onOpenChange(false);
    } catch (error) {
      console.error("Error creating fault:", error);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      const newAttachments: Attachment[] = Array.from(files).map(file => ({
        name: file.name,
        type: file.type,
        size: file.size,
      }));
      setAttachments(prev => [...prev, ...newAttachments]);
    }
  };

  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">إضافة عطل جديد</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <Tabs defaultValue="basic-info" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="basic-info" className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  المعلومات الأساسية
                  <span className="bg-blue-500 text-white text-xs rounded-full px-2 py-0.5 mr-1">
                    1
                  </span>
                </TabsTrigger>
                <TabsTrigger value="attachments" className="flex items-center gap-2">
                  <Paperclip className="h-4 w-4" />
                  المرفقات
                  {attachments.length > 0 && (
                    <span className="bg-green-500 text-white text-xs rounded-full px-2 py-0.5 mr-1">
                      {attachments.length}
                    </span>
                  )}
                </TabsTrigger>
                <TabsTrigger value="videos" className="flex items-center gap-2">
                  <Video className="h-4 w-4" />
                  الفيديوهات
                  {videosBeforeMaintenance.length > 0 && (
                    <span className="bg-purple-500 text-white text-xs rounded-full px-2 py-0.5 mr-1">
                      {videosBeforeMaintenance.length}
                    </span>
                  )}
                </TabsTrigger>
              </TabsList>

              <div className="max-h-[60vh] overflow-y-auto">
                <TabsContent value="basic-info" className="space-y-4 mt-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField control={form.control} name="customerName" render={({ field }) => (
                      <FormItem>
                        <FormLabel>اسم العميل</FormLabel>
                        <FormControl>
                          <Input placeholder="مثال: مركز النور الطبي" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} />
                    <FormField control={form.control} name="customerPhone" render={({ field }) => (
                      <FormItem>
                        <FormLabel>رقم الهاتف</FormLabel>
                        <FormControl>
                          <Input placeholder="01234567890" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} />
                    <FormField control={form.control} name="customerEmail" render={({ field }) => (
                      <FormItem>
                        <FormLabel>البريد الإلكتروني (اختياري)</FormLabel>
                        <FormControl>
                          <Input placeholder="<EMAIL>" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} />
                    <FormField control={form.control} name="facilityAddress" render={({ field }) => (
                      <FormItem>
                        <FormLabel>عنوان المنشأة</FormLabel>
                        <FormControl>
                          <Input placeholder="123 شارع النصر، القاهرة" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} />
                    <FormField control={form.control} name="deviceType" render={({ field }) => (
                      <FormItem>
                        <FormLabel>نوع الجهاز</FormLabel>
                        <FormControl>
                          <Input placeholder="مثال: جهاز ليزر لإزالة الشعر" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} />
                    <FormField control={form.control} name="serialNumber" render={({ field }) => (
                      <FormItem>
                        <FormLabel>الرقم التسلسلي (اختياري)</FormLabel>
                        <FormControl>
                          <Input placeholder="LHR-2024-001" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} />
                  </div>
                  
                  <FormField control={form.control} name="priority" render={({ field }) => (
                    <FormItem>
                      <FormLabel>أولوية العطل</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="اختر أولوية العطل" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="عاجل">عاجل</SelectItem>
                          <SelectItem value="متوسط">متوسط</SelectItem>
                          <SelectItem value="عادي">عادي</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )} />

                  <FormField control={form.control} name="faultDescription" render={({ field }) => (
                    <FormItem>
                      <FormLabel>وصف العطل</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="اكتب وصفاً مفصلاً للعطل..."
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )} />
                </TabsContent>

                <TabsContent value="attachments" className="space-y-4 mt-4">
                  <div className="space-y-4">
                    <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6">
                      <div className="text-center">
                        <Paperclip className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <Label htmlFor="attachments" className="text-lg font-medium">رفع المرفقات</Label>
                        <p className="text-sm text-muted-foreground mt-2">
                          اختر الصور أو المستندات المتعلقة بالعطل
                        </p>
                        <Input
                          id="attachments"
                          type="file"
                          multiple
                          accept="image/*,.pdf,.doc,.docx"
                          onChange={handleFileUpload}
                          className="mt-4"
                        />
                      </div>
                    </div>

                    {attachments.length > 0 && (
                      <div className="space-y-2">
                        <h4 className="font-medium">المرفقات المحددة:</h4>
                        <div className="space-y-2">
                          {attachments.map((attachment, index) => (
                            <div key={index} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                              <div className="flex items-center space-x-3 space-x-reverse">
                                <Paperclip className="h-4 w-4 text-muted-foreground" />
                                <div>
                                  <p className="text-sm font-medium">{attachment.name}</p>
                                  <p className="text-xs text-muted-foreground">
                                    {(attachment.size / 1024 / 1024).toFixed(2)} MB
                                  </p>
                                </div>
                              </div>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => removeAttachment(index)}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="videos" className="space-y-4 mt-4">
                  <div className="text-center p-8 border-2 border-dashed border-muted-foreground/25 rounded-lg">
                    <Video className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">فيديوهات قبل الصيانة</h3>
                    <p className="text-sm text-muted-foreground">
                      يمكنك رفع فيديوهات توضح حالة الجهاز قبل بدء الصيانة
                    </p>
                    <p className="text-xs text-muted-foreground mt-2">
                      سيتم إضافة هذه الميزة قريباً
                    </p>
                  </div>
                </TabsContent>
              </div>
            </Tabs>

            <DialogFooter className="pt-4">
              <DialogClose asChild>
                <Button type="button" variant="secondary">إلغاء</Button>
              </DialogClose>
              <Button type="submit">حفظ العطل</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default CreateFaultDialog;
