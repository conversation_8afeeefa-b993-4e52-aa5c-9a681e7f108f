import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Fault, DeviceType, FaultPriority, Attachment } from "@/types";
import { showSuccess } from "@/utils/toast";
import { useData } from "../../context/DataContext";
import { Label } from "../ui/label";
import { <PERSON>clip, X } from "lucide-react";

const priorities: [FaultPriority, ...FaultPriority[]] = ["عاجل", "متوسط", "عادي"];

const formSchema = z.object({
  customerName: z.string().min(2, { message: "الاسم مطلوب." }),
  customerPhone: z.string().min(10, { message: "رقم هاتف صالح مطلوب." }),
  customerEmail: z.string().email({ message: "بريد إلكتروني غير صالح." }),
  facilityAddress: z.string().min(5, { message: "العنوان مطلوب." }),
  deviceType: z.string().min(2, { message: "نوع الجهاز مطلوب." }),
  serialNumber: z.string().optional(),
  description: z.string().min(10, { message: "يجب ألا يقل الوصف عن 10 أحرف." }),
  priority: z.enum(priorities, { required_error: "الرجاء تحديد الأولوية." }),
});

interface EditFaultDialogProps {
  fault: Fault;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function EditFaultDialog({ fault, open, onOpenChange }: EditFaultDialogProps) {
  const { updateFault } = useData();
  const [attachments, setAttachments] = useState<Attachment[]>([]);
  const [newFiles, setNewFiles] = useState<File[]>([]);
  
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
  });

  useEffect(() => {
    if (open) {
      const defaultValues: z.infer<typeof formSchema> = {
        customerName: fault.customerName,
        customerPhone: fault.customerPhone,
        customerEmail: fault.customerEmail,
        facilityAddress: fault.facilityAddress,
        deviceType: fault.deviceType,
        serialNumber: fault.serialNumber || '',
        description: fault.description,
        priority: fault.priority,
      };
      form.reset(defaultValues);
      setAttachments(fault.attachments || []);
      setNewFiles([]);
    }
  }, [fault, open, form, setAttachments, setNewFiles]);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      setNewFiles(prev => [...prev, ...Array.from(event.target.files!)]);
    }
  };

  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const removeNewFile = (index: number) => {
    setNewFiles(prev => prev.filter((_, i) => i !== index));
  };

  function onSubmit(values: z.infer<typeof formSchema>) {
    const newAttachmentData: Attachment[] = newFiles.map(file => ({
      name: file.name,
      type: file.type,
      size: file.size,
    }));
    const finalAttachments = [...attachments, ...newAttachmentData];
    updateFault(fault.id, { ...values, attachments: finalAttachments });
    showSuccess("تم تحديث بيانات العطل بنجاح!");
    onOpenChange(false);
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[625px]">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <DialogHeader>
              <DialogTitle>تعديل بيانات العطل</DialogTitle>
              <DialogDescription>
                قم بتحديث المعلومات المطلوبة ثم اضغط على حفظ التغييرات.
              </DialogDescription>
            </DialogHeader>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-4 max-h-[70vh] overflow-y-auto px-2">
              <FormField control={form.control} name="customerName" render={({ field }) => (
                <FormItem><FormLabel>اسم العميل</FormLabel><FormControl><Input {...field} /></FormControl><FormMessage /></FormItem>
              )} />
              <FormField control={form.control} name="customerPhone" render={({ field }) => (
                <FormItem><FormLabel>رقم الهاتف</FormLabel><FormControl><Input {...field} /></FormControl><FormMessage /></FormItem>
              )} />
              <FormField control={form.control} name="customerEmail" render={({ field }) => (
                <FormItem className="md:col-span-2"><FormLabel>البريد الإلكتروني</FormLabel><FormControl><Input {...field} /></FormControl><FormMessage /></FormItem>
              )} />
              <FormField control={form.control} name="facilityAddress" render={({ field }) => (
                <FormItem className="md:col-span-2"><FormLabel>عنوان المنشأة</FormLabel><FormControl><Input {...field} /></FormControl><FormMessage /></FormItem>
              )} />
              <FormField control={form.control} name="deviceType" render={({ field }) => (
                <FormItem><FormLabel>نوع الجهاز</FormLabel><FormControl><Input {...field} /></FormControl><FormMessage /></FormItem>
              )} />
              <FormField control={form.control} name="serialNumber" render={({ field }) => (
                <FormItem><FormLabel>الرقم التسلسلي (اختياري)</FormLabel><FormControl><Input {...field} /></FormControl><FormMessage /></FormItem>
              )} />
              <FormField control={form.control} name="priority" render={({ field }) => (
                <FormItem className="md:col-span-2"><FormLabel>الأولوية</FormLabel><Select onValueChange={field.onChange} defaultValue={field.value}><FormControl><SelectTrigger><SelectValue /></SelectTrigger></FormControl><SelectContent>{priorities.map(p => <SelectItem key={p} value={p}>{p}</SelectItem>)}</SelectContent></Select><FormMessage /></FormItem>
              )} />
              <FormField control={form.control} name="description" render={({ field }) => (
                <FormItem className="md:col-span-2"><FormLabel>وصف العطل</FormLabel><FormControl><Textarea className="resize-none" {...field} /></FormControl><FormMessage /></FormItem>
              )} />
               <div className="md:col-span-2 space-y-2">
                <Label>المرفقات الحالية</Label>
                <div className="space-y-2">
                  {attachments.map((att, index) => (
                    <div key={index} className="flex items-center justify-between p-2 rounded-md bg-muted">
                      <div className="flex items-center gap-2">
                        <Paperclip className="h-4 w-4" />
                        <span className="text-sm font-medium">{att.name}</span>
                      </div>
                      <Button type="button" variant="ghost" size="icon" onClick={() => removeAttachment(index)}>
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                  {attachments.length === 0 && <p className="text-sm text-muted-foreground">لا توجد مرفقات حالية.</p>}
                </div>
                <Label htmlFor="attachments">إضافة مرفقات جديدة</Label>
                <Input id="attachments" type="file" multiple onChange={handleFileChange} className="block w-full text-sm text-slate-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-violet-50 file:text-violet-700 hover:file:bg-violet-100"/>
                <div className="space-y-2">
                  {newFiles.map((file, index) => (
                    <div key={index} className="flex items-center justify-between p-2 rounded-md bg-muted">
                      <div className="flex items-center gap-2">
                        <Paperclip className="h-4 w-4" />
                        <span className="text-sm font-medium">{file.name}</span>
                      </div>
                      <Button type="button" variant="ghost" size="icon" onClick={() => removeNewFile(index)}>
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            <DialogFooter className="pt-4">
              <DialogClose asChild><Button type="button" variant="secondary">إلغاء</Button></DialogClose>
              <Button type="submit">حفظ التغييرات</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}