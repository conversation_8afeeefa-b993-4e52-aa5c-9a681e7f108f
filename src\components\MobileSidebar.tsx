import { useState } from "react";
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet";
import { But<PERSON> } from "@/components/ui/button";
import { Menu, Building } from "lucide-react";
import { SidebarNav } from "./SidebarNav";

export const MobileSidebar = () => {
    const [open, setOpen] = useState(false);

    return (
        <Sheet open={open} onOpenChange={setOpen}>
            <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className="lg:hidden">
                    <Menu className="h-6 w-6" />
                    <span className="sr-only">فتح القائمة</span>
                </Button>
            </SheetTrigger>
            <SheetContent side="right" className="p-0 w-64 bg-card">
                <div className="flex items-center justify-center h-16 border-b border-border">
                    <Building className="h-6 w-6 text-primary" />
                    <h1 className="ms-2 text-xl font-bold text-primary">أواسيس فارما</h1>
                </div>
                <SidebarNav onLinkClick={() => setOpen(false)} />
            </SheetContent>
        </Sheet>
    );
}