import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Printer } from "lucide-react";
import { Fault } from "@/types";
import { format } from "date-fns";
import { ar } from "date-fns/locale";
import { useData } from "@/context/DataContext";

interface PrintFaultReportProps {
  fault: Fault;
}

export function PrintFaultReport({ fault }: PrintFaultReportProps) {
  const [isPrinting, setIsPrinting] = useState(false);
  const { faults } = useData();

  // الحصول على جميع أعطال العميل (دورة الصيانة الكاملة)
  const customerFaults = faults.filter(f => 
    f.customerName === fault.customerName || 
    f.customerPhone === fault.customerPhone ||
    f.customerEmail === fault.customerEmail
  ).sort((a, b) => new Date(a.reportedAt).getTime() - new Date(b.reportedAt).getTime());

  const printReport = () => {
    setIsPrinting(true);
    
    try {
      const printWindow = window.open('', '_blank');
      if (!printWindow) return;

      // حساب إحصائيات العميل
      const completedFaults = customerFaults.filter(f => f.status === 'تم الإصلاح');
      const pendingFaults = customerFaults.filter(f => f.status === 'مفتوح' || f.status === 'قيد المعالجة');
      const totalFaults = customerFaults.length;

      // حساب متوسط وقت الإصلاح
      const avgRepairTime = completedFaults.length > 0 
        ? completedFaults.reduce((acc, f) => {
            if (f.completedAt) {
              const hours = (new Date(f.completedAt).getTime() - new Date(f.reportedAt).getTime()) / (1000 * 60 * 60);
              return acc + hours;
            }
            return acc;
          }, 0) / completedFaults.length
        : 0;

      // أكثر الأجهزة أعطالاً
      const deviceCounts = customerFaults.reduce((acc, f) => {
        acc[f.deviceType] = (acc[f.deviceType] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      
      const mostFrequentDevice = Object.entries(deviceCounts)
        .sort((a, b) => b[1] - a[1])[0]?.[0] || "لا يوجد";

      printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
          <meta charset="UTF-8">
          <title>تقرير دورة الصيانة - ${fault.customerName}</title>
          <style>
            body { 
              font-family: Arial, sans-serif; 
              margin: 20px; 
              direction: rtl; 
              line-height: 1.6;
            }
            .header { 
              text-align: center; 
              margin-bottom: 30px; 
              border-bottom: 2px solid #333;
              padding-bottom: 20px;
            }
            .company-logo {
              font-size: 24px;
              font-weight: bold;
              color: #2563eb;
              margin-bottom: 10px;
            }
            .customer-info { 
              background: #f8f9fa; 
              padding: 20px; 
              border-radius: 8px; 
              margin-bottom: 30px;
              border: 1px solid #dee2e6;
            }
            .stats { 
              display: grid; 
              grid-template-columns: repeat(4, 1fr); 
              gap: 15px; 
              margin-bottom: 30px; 
            }
            .stat-card { 
              background: #f9fafb; 
              padding: 15px; 
              border-radius: 8px; 
              text-align: center;
              border: 1px solid #e5e7eb;
            }
            .stat-number {
              font-size: 24px;
              font-weight: bold;
              color: #1f2937;
              margin-bottom: 5px;
            }
            .stat-label {
              font-size: 12px;
              color: #6b7280;
            }
            table { 
              width: 100%; 
              border-collapse: collapse; 
              margin-top: 20px; 
              font-size: 12px;
            }
            th, td { 
              border: 1px solid #ddd; 
              padding: 8px; 
              text-align: right; 
            }
            th { 
              background-color: #f3f4f6; 
              font-weight: bold;
            }
            .current-fault {
              background-color: #fef3c7 !important;
              font-weight: bold;
            }
            .status-completed { background: #d1fae5; color: #065f46; }
            .status-pending { background: #fef3c7; color: #92400e; }
            .status-open { background: #fee2e2; color: #dc2626; }
            .priority-urgent { background: #fee2e2; color: #dc2626; padding: 2px 6px; border-radius: 4px; }
            .priority-medium { background: #fef3c7; color: #d97706; padding: 2px 6px; border-radius: 4px; }
            .priority-normal { background: #f0f9ff; color: #2563eb; padding: 2px 6px; border-radius: 4px; }
            .footer {
              margin-top: 40px;
              text-align: center;
              color: #666;
              font-size: 12px;
              border-top: 1px solid #ddd;
              padding-top: 20px;
            }
            .section-title {
              font-size: 18px;
              font-weight: bold;
              margin: 30px 0 15px 0;
              color: #1f2937;
              border-bottom: 1px solid #e5e7eb;
              padding-bottom: 5px;
            }
            @media print { 
              body { margin: 0; }
              .stat-card { break-inside: avoid; }
              table { break-inside: avoid; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="company-logo">نظام إدارة الأعطال</div>
            <h1>تقرير دورة الصيانة الكاملة</h1>
            <p>تاريخ التقرير: ${format(new Date(), 'dd/MM/yyyy - HH:mm', { locale: ar })}</p>
            <p>العطل الحالي: #${fault.id}</p>
          </div>
          
          <div class="customer-info">
            <h2 style="margin-top: 0; color: #1f2937;">بيانات العميل</h2>
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
              <div><strong>اسم العميل:</strong> ${fault.customerName}</div>
              <div><strong>رقم الهاتف:</strong> ${fault.customerPhone}</div>
              <div><strong>البريد الإلكتروني:</strong> ${fault.customerEmail}</div>
              <div><strong>عنوان المنشأة:</strong> ${fault.facilityAddress}</div>
            </div>
          </div>

          <div class="section-title">إحصائيات دورة الصيانة</div>
          <div class="stats">
            <div class="stat-card">
              <div class="stat-number">${totalFaults}</div>
              <div class="stat-label">إجمالي الأعطال</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">${completedFaults.length}</div>
              <div class="stat-label">أعطال مكتملة</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">${pendingFaults.length}</div>
              <div class="stat-label">أعطال معلقة</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">${avgRepairTime.toFixed(1)} ساعة</div>
              <div class="stat-label">متوسط وقت الإصلاح</div>
            </div>
          </div>

          <div class="section-title">تفاصيل العطل الحالي</div>
          <div style="background: #fef3c7; padding: 20px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #f59e0b;">
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; margin-bottom: 15px;">
              <div><strong>رقم العطل:</strong> ${fault.id}</div>
              <div><strong>نوع الجهاز:</strong> ${fault.deviceType}</div>
              <div><strong>الرقم التسلسلي:</strong> ${fault.serialNumber || 'غير محدد'}</div>
              <div><strong>الأولوية:</strong> <span class="priority-${fault.priority === 'عاجل' ? 'urgent' : fault.priority === 'متوسط' ? 'medium' : 'normal'}">${fault.priority}</span></div>
              <div><strong>الحالة:</strong> ${fault.status}</div>
              <div><strong>المهندس المسؤول:</strong> ${fault.engineer || 'لم يتم التعيين'}</div>
              <div><strong>تاريخ الإبلاغ:</strong> ${format(new Date(fault.reportedAt), 'dd/MM/yyyy - HH:mm', { locale: ar })}</div>
              ${fault.completedAt ? `<div><strong>تاريخ الإنجاز:</strong> ${format(new Date(fault.completedAt), 'dd/MM/yyyy - HH:mm', { locale: ar })}</div>` : ''}
            </div>
            <div><strong>وصف المشكلة:</strong></div>
            <div style="background: white; padding: 10px; border-radius: 4px; margin-top: 5px;">${fault.description}</div>
          </div>

          <div class="section-title">سجل دورة الصيانة الكاملة</div>
          <table>
            <thead>
              <tr>
                <th>رقم العطل</th>
                <th>نوع الجهاز</th>
                <th>الأولوية</th>
                <th>الحالة</th>
                <th>المهندس</th>
                <th>تاريخ الإبلاغ</th>
                <th>تاريخ الإنجاز</th>
                <th>مدة الإصلاح</th>
              </tr>
            </thead>
            <tbody>
              ${customerFaults.map(f => {
                const repairDuration = f.completedAt 
                  ? `${Math.round((new Date(f.completedAt).getTime() - new Date(f.reportedAt).getTime()) / (1000 * 60 * 60))} ساعة`
                  : '-';
                
                const isCurrentFault = f.id === fault.id;
                
                return `
                  <tr class="${isCurrentFault ? 'current-fault' : ''}">
                    <td>${f.id}${isCurrentFault ? ' (الحالي)' : ''}</td>
                    <td>${f.deviceType}</td>
                    <td>
                      <span class="priority-${f.priority === 'عاجل' ? 'urgent' : f.priority === 'متوسط' ? 'medium' : 'normal'}">
                        ${f.priority}
                      </span>
                    </td>
                    <td class="status-${f.status === 'تم الإصلاح' ? 'completed' : f.status === 'مفتوح' ? 'open' : 'pending'}">${f.status}</td>
                    <td>${f.engineer || '-'}</td>
                    <td>${format(new Date(f.reportedAt), 'dd/MM/yyyy', { locale: ar })}</td>
                    <td>${f.completedAt ? format(new Date(f.completedAt), 'dd/MM/yyyy', { locale: ar }) : '-'}</td>
                    <td>${repairDuration}</td>
                  </tr>
                `;
              }).join('')}
            </tbody>
          </table>

          <div class="section-title">ملخص الأجهزة</div>
          <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
            <p><strong>الجهاز الأكثر أعطالاً:</strong> ${mostFrequentDevice}</p>
            <p><strong>أنواع الأجهزة:</strong></p>
            <ul>
              ${Object.entries(deviceCounts).map(([device, count]) => 
                `<li>${device}: ${count} عطل</li>`
              ).join('')}
            </ul>
          </div>

          <div class="footer">
            <p>تم إنشاء هذا التقرير بواسطة نظام إدارة الأعطال</p>
            <p>هذا التقرير يحتوي على دورة الصيانة الكاملة للعميل منذ بداية التعامل</p>
          </div>
        </body>
        </html>
      `);
      
      printWindow.document.close();
      printWindow.focus();
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
      }, 250);

    } catch (error) {
      console.error('خطأ في طباعة التقرير:', error);
      alert('حدث خطأ أثناء تحضير التقرير للطباعة. يرجى المحاولة مرة أخرى.');
    } finally {
      setIsPrinting(false);
    }
  };

  return (
    <Button 
      onClick={printReport} 
      disabled={isPrinting}
      variant="outline"
      size="sm"
      className="gap-2"
    >
      {isPrinting ? (
        <>
          <Printer className="h-4 w-4 animate-pulse" />
          جاري التحضير...
        </>
      ) : (
        <>
          <Printer className="h-4 w-4" />
          طباعة تقرير دورة الصيانة
        </>
      )}
    </Button>
  );
}
