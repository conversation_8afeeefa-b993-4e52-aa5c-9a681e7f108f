@echo off
echo Starting Fault Management System...
echo ================================

REM Change to the project directory
cd /d "%~dp0"

REM Try different methods to start the development server
echo Attempting to start the development server...

REM Method 1: Try npm run dev
echo Method 1: Using npm run dev
powershell -ExecutionPolicy Bypass -Command "npm run dev"

REM If that fails, try method 2
if %errorlevel% neq 0 (
    echo Method 1 failed, trying Method 2: Using npx vite
    powershell -ExecutionPolicy Bypass -Command "npx vite"
)

REM If that fails, try method 3
if %errorlevel% neq 0 (
    echo Method 2 failed, trying Method 3: Direct vite execution
    powershell -ExecutionPolicy Bypass -Command "node_modules\.bin\vite"
)

REM If all methods fail
if %errorlevel% neq 0 (
    echo All methods failed. Please check if Node.js and npm are installed.
    echo Press any key to exit...
    pause >nul
)
