import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Download, FileText } from "lucide-react";
import { Fault } from "@/types";
import { format } from "date-fns";
import { ar } from "date-fns/locale";

interface ExportToPDFProps {
  data: Fault[];
  dateRange?: {
    from?: Date;
    to?: Date;
  };
  totalCompleted: number;
  averageTime: string;
  mostFrequentDevice: string;
}

export function ExportToPDF({
  data,
  dateRange,
  totalCompleted,
  averageTime,
  mostFrequentDevice
}: ExportToPDFProps) {
  const [isExporting, setIsExporting] = useState(false);

  const printReport = () => {
    const dateRangeText = dateRange?.from
      ? `من ${format(dateRange.from, 'dd/MM/yyyy', { locale: ar })} ${dateRange.to ? `إلى ${format(dateRange.to, 'dd/MM/yyyy', { locale: ar })}` : ''}`
      : 'جميع الفترات';

    const printWindow = window.open('', '_blank');
    if (!printWindow) return;

    printWindow.document.write(`
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <title>تقرير الأعطال المنجزة</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
          .header { text-align: center; margin-bottom: 30px; }
          .stats { display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin-bottom: 30px; }
          .stat-card { background: #f9fafb; padding: 20px; border-radius: 8px; text-align: center; }
          table { width: 100%; border-collapse: collapse; margin-top: 20px; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
          th { background-color: #f3f4f6; }
          .priority-urgent { background: #fee2e2; color: #dc2626; padding: 2px 6px; border-radius: 4px; }
          .priority-medium { background: #fef3c7; color: #d97706; padding: 2px 6px; border-radius: 4px; }
          .priority-normal { background: #f0f9ff; color: #2563eb; padding: 2px 6px; border-radius: 4px; }
          @media print { body { margin: 0; } }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>تقرير الأعطال المنجزة</h1>
          <p>تاريخ التقرير: ${format(new Date(), 'dd/MM/yyyy', { locale: ar })}</p>
          <p>الفترة الزمنية: ${dateRangeText}</p>
        </div>

        <div class="stats">
          <div class="stat-card">
            <h3>إجمالي التقارير المنجزة</h3>
            <p style="font-size: 24px; font-weight: bold;">${totalCompleted}</p>
          </div>
          <div class="stat-card">
            <h3>متوسط وقت الإصلاح</h3>
            <p style="font-size: 24px; font-weight: bold;">${averageTime}</p>
          </div>
          <div class="stat-card">
            <h3>الجهاز الأكثر أعطالاً</h3>
            <p style="font-size: 18px; font-weight: bold;">${mostFrequentDevice}</p>
          </div>
        </div>

        <h2>تفاصيل الأعطال المنجزة</h2>
        <table>
          <thead>
            <tr>
              <th>رقم العطل</th>
              <th>اسم العميل</th>
              <th>نوع الجهاز</th>
              <th>الأولوية</th>
              <th>المهندس</th>
              <th>تاريخ الإبلاغ</th>
              <th>تاريخ الإنجاز</th>
            </tr>
          </thead>
          <tbody>
            ${data.map(fault => `
              <tr>
                <td>${fault.id}</td>
                <td>${fault.customerName}</td>
                <td>${fault.deviceType}</td>
                <td>
                  <span class="priority-${fault.priority === 'عاجل' ? 'urgent' : fault.priority === 'متوسط' ? 'medium' : 'normal'}">
                    ${fault.priority}
                  </span>
                </td>
                <td>${fault.engineer || '-'}</td>
                <td>${format(new Date(fault.reportedAt), 'dd/MM/yyyy', { locale: ar })}</td>
                <td>${fault.completedAt ? format(new Date(fault.completedAt), 'dd/MM/yyyy', { locale: ar }) : '-'}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>

        <div style="margin-top: 30px; text-align: center; color: #666; font-size: 12px;">
          <p>تم إنشاء هذا التقرير بواسطة نظام إدارة الأعطال</p>
        </div>
      </body>
      </html>
    `);

    printWindow.document.close();
    printWindow.focus();
    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 250);
  };

  const exportToPDF = async () => {
    setIsExporting(true);

    try {
      // استخدم الطباعة العادية مباشرة
      printReport();
    } catch (error) {
      console.error('خطأ في الطباعة:', error);
      alert('حدث خطأ أثناء تحضير التقرير للطباعة. يرجى المحاولة مرة أخرى.');
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <Button 
      onClick={exportToPDF} 
      disabled={isExporting}
      className="gap-2"
    >
      {isExporting ? (
        <>
          <FileText className="h-4 w-4 animate-spin" />
          جاري التحضير...
        </>
      ) : (
        <>
          <Download className="h-4 w-4" />
          طباعة التقرير
        </>
      )}
    </Button>
  );
}
