import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { PlusCircle, Paperclip, X, User, Video } from "lucide-react";
import { DeviceType, FaultPriority, Attachment, VideoAttachment } from "@/types";
import { showSuccess } from "@/utils/toast";
import { useData } from "../../context/DataContext";
import { Label } from "../ui/label";
import { VideoUpload } from "./VideoUpload";

const priorities: [FaultPriority, ...FaultPriority[]] = ["عاجل", "متوسط", "عادي"];

const formSchema = z.object({
  customerName: z.string().min(2, { message: "الاسم مطلوب." }),
  customerPhone: z.string().min(10, { message: "رقم هاتف صالح مطلوب." }),
  customerEmail: z.string().email({ message: "بريد إلكتروني غير صالح." }),
  facilityAddress: z.string().min(5, { message: "عنوان المنشأة مطلوب." }),
  deviceType: z.string().min(2, { message: "نوع الجهاز مطلوب." }),
  serialNumber: z.string().optional(),
  description: z.string().min(10, { message: "وصف العطل مطلوب (10 أحرف على الأقل)." }),
  priority: z.enum(priorities, { required_error: "الرجاء اختيار الأولوية." }),
});

const defaultFormValues: z.infer<typeof formSchema> = {
  customerName: "",
  customerPhone: "",
  customerEmail: "",
  facilityAddress: "",
  description: "",
  serialNumber: "",
  priority: "عادي",
  deviceType: "",
};

export function CreateFaultDialog() {
  const [open, setOpen] = useState(false);
  const [attachments, setAttachments] = useState<File[]>([]);
  const [videosBeforeMaintenance, setVideosBeforeMaintenance] = useState<VideoAttachment[]>([]);
  const { addFault } = useData();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: defaultFormValues,
  });

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      setAttachments(prev => [...prev, ...Array.from(event.target.files!)]);
    }
  };

  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  function onSubmit(values: z.infer<typeof formSchema>) {
    const attachmentData: Attachment[] = attachments.map(file => ({
      name: file.name,
      type: file.type,
      size: file.size,
    }));

    addFault({
      ...values,
      attachments: attachmentData,
      videosBeforeMaintenance: videosBeforeMaintenance
    });
    showSuccess("تم تسجيل العطل بنجاح!");
    form.reset(defaultFormValues);
    setAttachments([]);
    setVideosBeforeMaintenance([]);
    setOpen(false);
  }

  return (
    <Dialog open={open} onOpenChange={(isOpen) => {
      setOpen(isOpen);
      if (!isOpen) {
        form.reset(defaultFormValues);
        setAttachments([]);
        setVideosBeforeMaintenance([]);
      }
    }}>
      <DialogTrigger asChild>
        <Button>
          <PlusCircle className="me-2 h-4 w-4" /> إضافة عطل جديد
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh]">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <DialogHeader>
              <DialogTitle>تسجيل عطل جديد</DialogTitle>
              <DialogDescription>
                يرجى ملء جميع الحقول المطلوبة. سيتم إنشاء رقم مرجعي فريد بعد الحفظ.
              </DialogDescription>
            </DialogHeader>

            <Tabs defaultValue="basic-info" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="basic-info" className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  المعلومات الأساسية
                </TabsTrigger>
                <TabsTrigger value="attachments" className="flex items-center gap-2">
                  <Paperclip className="h-4 w-4" />
                  المرفقات
                  {attachments.length > 0 && (
                    <span className="bg-primary text-primary-foreground text-xs px-2 py-1 rounded-full">
                      {attachments.length}
                    </span>
                  )}
                </TabsTrigger>
                <TabsTrigger value="videos" className="flex items-center gap-2">
                  <Video className="h-4 w-4" />
                  الفيديوهات
                  {videosBeforeMaintenance.length > 0 && (
                    <span className="bg-primary text-primary-foreground text-xs px-2 py-1 rounded-full">
                      {videosBeforeMaintenance.length}
                    </span>
                  )}
                </TabsTrigger>
              </TabsList>

              <div className="max-h-[60vh] overflow-y-auto">
                <TabsContent value="basic-info" className="space-y-4 mt-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField control={form.control} name="customerName" render={({ field }) => (
                      <FormItem>
                        <FormLabel>اسم العميل</FormLabel>
                        <FormControl>
                          <Input placeholder="مثال: مركز النور الطبي" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} />
                    <FormField control={form.control} name="customerPhone" render={({ field }) => (
                      <FormItem>
                        <FormLabel>رقم الهاتف</FormLabel>
                        <FormControl>
                          <Input placeholder="01xxxxxxxxx" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} />
                    <FormField control={form.control} name="customerEmail" render={({ field }) => (
                      <FormItem className="md:col-span-2">
                        <FormLabel>البريد الإلكتروني</FormLabel>
                        <FormControl>
                          <Input placeholder="<EMAIL>" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} />
                    <FormField control={form.control} name="facilityAddress" render={({ field }) => (
                      <FormItem className="md:col-span-2">
                        <FormLabel>عنوان المنشأة</FormLabel>
                        <FormControl>
                          <Input placeholder="مثال: 123 شارع النصر، القاهرة" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} />
                    <FormField control={form.control} name="deviceType" render={({ field }) => (
                      <FormItem>
                        <FormLabel>نوع الجهاز</FormLabel>
                        <FormControl>
                          <Input placeholder="مثال: جهاز ليزر لإزالة الشعر" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} />
                    <FormField control={form.control} name="serialNumber" render={({ field }) => (
                      <FormItem>
                        <FormLabel>الرقم التسلسلي (اختياري)</FormLabel>
                        <FormControl>
                          <Input placeholder="SN-123456" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} />
                    <FormField control={form.control} name="priority" render={({ field }) => (
                      <FormItem className="md:col-span-2">
                        <FormLabel>الأولوية</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="حدد أولوية العطل" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {priorities.map(p => (
                              <SelectItem key={p} value={p}>{p}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )} />
                    <FormField control={form.control} name="description" render={({ field }) => (
                      <FormItem className="md:col-span-2">
                        <FormLabel>وصف العطل</FormLabel>
                        <FormControl>
                          <Textarea placeholder="صف المشكلة بالتفصيل..." className="resize-none" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} />
                  </div>
                </TabsContent>

                <TabsContent value="attachments" className="space-y-4 mt-4">
                  <div className="space-y-4">
                    <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6">
                      <div className="text-center">
                        <Paperclip className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <Label htmlFor="attachments" className="text-lg font-medium">رفع المرفقات</Label>
                        <p className="text-sm text-muted-foreground mb-4">يمكنك رفع صور أو مستندات متعلقة بالعطل</p>
                        <Input
                          id="attachments"
                          type="file"
                          multiple
                          onChange={handleFileChange}
                          className="block w-full text-sm text-slate-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-violet-50 file:text-violet-700 hover:file:bg-violet-100"
                        />
                      </div>
                    </div>
                    {attachments.length > 0 && (
                      <div className="space-y-2">
                        <Label>المرفقات المحددة ({attachments.length})</Label>
                        {attachments.map((file, index) => (
                          <div key={index} className="flex items-center justify-between p-3 rounded-md bg-muted">
                            <div className="flex items-center gap-2">
                              <Paperclip className="h-4 w-4" />
                              <span className="text-sm font-medium">{file.name}</span>
                              <span className="text-xs text-muted-foreground">({(file.size / 1024).toFixed(1)} KB)</span>
                            </div>
                            <Button type="button" variant="ghost" size="icon" onClick={() => removeAttachment(index)}>
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="videos" className="space-y-4 mt-4">
                  <VideoUpload
                    videos={videosBeforeMaintenance}
                    onVideosChange={setVideosBeforeMaintenance}
                    type="before"
                    title="فيديوهات قبل الصيانة (اختياري)"
                    description="يمكنك رفع فيديوهات توضح حالة الجهاز قبل بدء الصيانة"
                  />
                </TabsContent>
              </div>
            </Tabs>

            <DialogFooter className="pt-4">
              <DialogClose asChild>
                <Button type="button" variant="secondary">إلغاء</Button>
              </DialogClose>
              <Button type="submit">حفظ العطل</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}