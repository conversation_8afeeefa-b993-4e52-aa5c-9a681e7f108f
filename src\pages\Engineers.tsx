import { useData } from "../context/DataContext";
import { CreateEngineerDialog } from "@/components/engineers/CreateEngineerDialog";
import { EngineersDataTable } from "@/components/engineers/EngineersDataTable";
import { columns } from "@/components/engineers/columns";
import { useSearchParams } from "react-router-dom";
import { ColumnFiltersState } from "@tanstack/react-table";

const Engineers = () => {
  const { engineers } = useData();
  const [searchParams] = useSearchParams();

  const statusParam = searchParams.get("status");
  const initialColumnFilters: ColumnFiltersState = [];
  if (statusParam) {
    initialColumnFilters.push({
      id: 'status',
      value: [statusParam],
    });
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">إدارة المهندسين</h1>
        <CreateEngineerDialog />
      </div>
      <EngineersDataTable columns={columns} data={engineers} initialColumnFilters={initialColumnFilters} />
    </div>
  );
};

export default Engineers;