import { useData } from "../context/DataContext";
import { CreateEngineerDialog } from "@/components/engineers/CreateEngineerDialog";
import { EngineersDataTable } from "@/components/engineers/EngineersDataTable";
import { columns } from "@/components/engineers/columns";
import { useSearchParams } from "react-router-dom";
import { ColumnFiltersState } from "@tanstack/react-table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Users, UserCheck, UserX, Clock } from "lucide-react";

const Engineers = () => {
  const { engineers, faults } = useData();
  const [searchParams] = useSearchParams();

  const statusParam = searchParams.get("status");
  const initialColumnFilters: ColumnFiltersState = [];
  if (statusParam) {
    initialColumnFilters.push({
      id: 'status',
      value: [statusParam],
    });
  }

  // إحصائيات المهندسين
  const availableEngineers = engineers.filter(e => e.status === 'متاح').length;
  const busyEngineers = engineers.filter(e => e.status === 'مشغول').length;
  const onLeaveEngineers = engineers.filter(e => e.status === 'في إجازة').length;
  const totalEngineers = engineers.length;

  // إحصائيات الأعطال المسندة
  const assignedFaults = faults.filter(f => f.engineer).length;
  const unassignedFaults = faults.filter(f => !f.engineer && (f.status === 'مفتوح' || f.status === 'قيد المعالجة')).length;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">إدارة المهندسين</h1>
        <CreateEngineerDialog />
      </div>

      {/* إحصائيات المهندسين */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-0 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-700 dark:text-green-300">
              مهندسون متاحون
            </CardTitle>
            <UserCheck className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{availableEngineers}</div>
            <p className="text-xs text-green-600/70">جاهزون للعمل</p>
          </CardContent>
        </Card>

        <Card className="border-0 bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-950/20 dark:to-orange-950/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-yellow-700 dark:text-yellow-300">
              مهندسون مشغولون
            </CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{busyEngineers}</div>
            <p className="text-xs text-yellow-600/70">يعملون حالياً</p>
          </CardContent>
        </Card>

        <Card className="border-0 bg-gradient-to-br from-gray-50 to-slate-50 dark:from-gray-950/20 dark:to-slate-950/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-700 dark:text-gray-300">
              في إجازة
            </CardTitle>
            <UserX className="h-4 w-4 text-gray-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-600">{onLeaveEngineers}</div>
            <p className="text-xs text-gray-600/70">غير متاحين</p>
          </CardContent>
        </Card>

        <Card className="border-0 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-700 dark:text-blue-300">
              إجمالي المهندسين
            </CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{totalEngineers}</div>
            <p className="text-xs text-blue-600/70">في النظام</p>
          </CardContent>
        </Card>
      </div>

      {/* إحصائيات الأعطال */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card className="border-0 bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-950/20 dark:to-violet-950/20">
          <CardHeader>
            <CardTitle className="text-lg text-purple-700 dark:text-purple-300">الأعطال المسندة</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-purple-600 mb-2">{assignedFaults}</div>
            <p className="text-sm text-purple-600/70">عطل مسند للمهندسين</p>
          </CardContent>
        </Card>

        <Card className="border-0 bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-950/20 dark:to-pink-950/20">
          <CardHeader>
            <CardTitle className="text-lg text-red-700 dark:text-red-300">أعطال بانتظار التعيين</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-red-600 mb-2">{unassignedFaults}</div>
            <p className="text-sm text-red-600/70">عطل يحتاج تعيين مهندس</p>
          </CardContent>
        </Card>
      </div>

      <EngineersDataTable columns={columns} data={engineers} initialColumnFilters={initialColumnFilters} />
    </div>
  );
};

export default Engineers;