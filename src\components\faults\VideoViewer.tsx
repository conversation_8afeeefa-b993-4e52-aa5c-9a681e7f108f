import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { VideoAttachment } from "@/types";
import { Video, Play, Download, Calendar, FileVideo, Maximize2 } from "lucide-react";

interface VideoViewerProps {
  videosBeforeMaintenance?: VideoAttachment[];
  videosAfterMaintenance?: VideoAttachment[];
  title: string;
}

export function VideoViewer({ 
  videosBeforeMaintenance = [], 
  videosAfterMaintenance = [], 
  title 
}: VideoViewerProps) {
  const [selectedVideo, setSelectedVideo] = useState<VideoAttachment | null>(null);

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('ar-EG');
  };

  const VideoCard = ({ video, type }: { video: VideoAttachment; type: 'before' | 'after' }) => (
    <div className="border rounded-lg p-4 space-y-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <FileVideo className="h-5 w-5 text-blue-500" />
          <span className="font-medium truncate">{video.name}</span>
        </div>
        <Badge variant={type === 'before' ? 'secondary' : 'default'}>
          {type === 'before' ? 'قبل الصيانة' : 'بعد الصيانة'}
        </Badge>
      </div>
      
      <div className="flex items-center gap-4 text-sm text-muted-foreground">
        <div className="flex items-center gap-1">
          <Calendar className="h-4 w-4" />
          {formatDate(video.uploadedAt)}
        </div>
        <span>{formatFileSize(video.size)}</span>
      </div>

      <div className="flex items-center gap-2">
        <Dialog>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm" onClick={() => setSelectedVideo(video)}>
              <Play className="h-4 w-4 me-2" />
              تشغيل
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Video className="h-5 w-5" />
                {video.name}
              </DialogTitle>
            </DialogHeader>
            <div className="aspect-video">
              <video
                controls
                className="w-full h-full rounded-lg"
                src={video.url}
              >
                متصفحك لا يدعم تشغيل الفيديو.
              </video>
            </div>
          </DialogContent>
        </Dialog>

        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            const link = document.createElement('a');
            link.href = video.url;
            link.download = video.name;
            link.click();
          }}
        >
          <Download className="h-4 w-4 me-2" />
          تحميل
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={() => window.open(video.url, '_blank')}
        >
          <Maximize2 className="h-4 w-4 me-2" />
          فتح في نافذة جديدة
        </Button>
      </div>
    </div>
  );

  const hasVideos = videosBeforeMaintenance.length > 0 || videosAfterMaintenance.length > 0;

  if (!hasVideos) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Video className="h-5 w-5" />
            {title}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <Video className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>لا توجد فيديوهات مرفوعة لهذا العطل</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Video className="h-5 w-5" />
          {title}
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          إجمالي {videosBeforeMaintenance.length + videosAfterMaintenance.length} فيديو
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* فيديوهات قبل الصيانة */}
        {videosBeforeMaintenance.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium text-orange-600 flex items-center gap-2">
              <Video className="h-4 w-4" />
              فيديوهات قبل الصيانة ({videosBeforeMaintenance.length})
            </h4>
            <div className="grid gap-3">
              {videosBeforeMaintenance.map((video) => (
                <VideoCard key={video.id} video={video} type="before" />
              ))}
            </div>
          </div>
        )}

        {/* فيديوهات بعد الصيانة */}
        {videosAfterMaintenance.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium text-green-600 flex items-center gap-2">
              <Video className="h-4 w-4" />
              فيديوهات بعد الصيانة ({videosAfterMaintenance.length})
            </h4>
            <div className="grid gap-3">
              {videosAfterMaintenance.map((video) => (
                <VideoCard key={video.id} video={video} type="after" />
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
