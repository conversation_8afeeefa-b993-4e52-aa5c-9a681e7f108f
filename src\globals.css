@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* خلفية متدرجة حديثة */
    --background: 240 10% 98%;
    --foreground: 240 10% 15%;

    /* كروت بتصميم حديث */
    --card: 0 0% 100%;
    --card-foreground: 240 10% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 15%;

    /* ألوان أساسية جذابة - أزرق/بنفسجي */
    --primary: 262 83% 58%;
    --primary-foreground: 0 0% 100%;

    /* ألوان ثانوية ناعمة */
    --secondary: 240 5% 96%;
    --secondary-foreground: 240 6% 10%;

    /* ألوان مكتومة أنيقة */
    --muted: 240 5% 96%;
    --muted-foreground: 240 4% 46%;

    /* لون التمييز - أخضر نعناعي */
    --accent: 142 76% 36%;
    --accent-foreground: 0 0% 100%;

    /* لون التحذير - أحمر حديث */
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    /* حدود ناعمة */
    --border: 240 6% 90%;
    --input: 240 6% 90%;
    --ring: 262 83% 58%;

    --radius: 0.75rem;

    /* شريط جانبي حديث */
    --sidebar-background: 240 10% 98%;
    --sidebar-foreground: 240 10% 15%;
    --sidebar-primary: 262 83% 58%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 5% 96%;
    --sidebar-accent-foreground: 240 10% 15%;
    --sidebar-border: 240 6% 90%;
    --sidebar-ring: 262 83% 58%;

    /* ألوان إضافية للتطبيق */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 100%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;
    --info: 199 89% 48%;
    --info-foreground: 0 0% 100%;
  }

  .dark {
    /* خلفية مظلمة حديثة */
    --background: 240 10% 8%;
    --foreground: 240 5% 96%;

    /* كروت مظلمة أنيقة */
    --card: 240 10% 12%;
    --card-foreground: 240 5% 96%;

    --popover: 240 10% 12%;
    --popover-foreground: 240 5% 96%;

    /* ألوان أساسية مشرقة في الوضع المظلم */
    --primary: 262 83% 68%;
    --primary-foreground: 240 10% 8%;

    /* ألوان ثانوية مظلمة */
    --secondary: 240 4% 16%;
    --secondary-foreground: 240 5% 96%;

    /* ألوان مكتومة مظلمة */
    --muted: 240 4% 16%;
    --muted-foreground: 240 5% 65%;

    /* لون التمييز في الوضع المظلم */
    --accent: 142 76% 46%;
    --accent-foreground: 240 10% 8%;

    /* لون التحذير في الوضع المظلم */
    --destructive: 0 84% 70%;
    --destructive-foreground: 240 10% 8%;

    /* حدود مظلمة */
    --border: 240 4% 20%;
    --input: 240 4% 20%;
    --ring: 262 83% 68%;

    /* شريط جانبي مظلم */
    --sidebar-background: 240 10% 8%;
    --sidebar-foreground: 240 5% 96%;
    --sidebar-primary: 262 83% 68%;
    --sidebar-primary-foreground: 240 10% 8%;
    --sidebar-accent: 240 4% 16%;
    --sidebar-accent-foreground: 240 5% 96%;
    --sidebar-border: 240 4% 20%;
    --sidebar-ring: 262 83% 68%;

    /* ألوان إضافية للوضع المظلم */
    --success: 142 76% 46%;
    --success-foreground: 240 10% 8%;
    --warning: 38 92% 60%;
    --warning-foreground: 240 10% 8%;
    --info: 199 89% 58%;
    --info-foreground: 240 10% 8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(240 10% 96%) 100%);
    min-height: 100vh;
  }
}

@layer components {
  /* تأثيرات حديثة للكروت */
  .modern-card {
    @apply bg-card/80 backdrop-blur-sm border border-border/50 shadow-lg hover:shadow-xl transition-all duration-300;
    background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--card)/0.95) 100%);
  }

  /* أزرار حديثة */
  .modern-button {
    @apply relative overflow-hidden transition-all duration-300;
  }

  .modern-button::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent;
    transform: translateX(-100%);
    transition: transform 0.6s;
  }

  .modern-button:hover::before {
    transform: translateX(100%);
  }

  /* تدرجات ملونة للحالات */
  .status-gradient-open {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  }

  .status-gradient-progress {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  }

  .status-gradient-completed {
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  }

  /* تأثيرات الحركة */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  /* تأثيرات الخلفية */
  .glass-effect {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .gradient-border {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--accent)) 100%);
    padding: 1px;
    border-radius: calc(var(--radius) + 1px);
  }

  .gradient-border > * {
    @apply bg-background rounded-[calc(var(--radius))] w-full h-full;
  }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* تحسينات للشريط الجانبي */
.sidebar-modern {
  background: linear-gradient(180deg, hsl(var(--sidebar-background)) 0%, hsl(var(--sidebar-background)/0.95) 100%);
  backdrop-filter: blur(10px);
}

/* تحسينات للجداول */
.table-modern {
  @apply bg-card/50 backdrop-blur-sm;
}

.table-modern th {
  background: linear-gradient(135deg, hsl(var(--muted)) 0%, hsl(var(--muted)/0.8) 100%);
}

.table-modern tr:hover {
  @apply bg-muted/30 transition-colors duration-200;
}
