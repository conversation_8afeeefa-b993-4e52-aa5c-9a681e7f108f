import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, User, Phone, Mail, MapPin, Calendar, Wrench, BarChart3 } from "lucide-react";
import { useData } from "@/context/DataContext";

const EngineerDetails = () => {
  const { id } = useParams<{ id: string }>();
  const { engineers, faults } = useData();
  
  const engineer = engineers.find(e => e.id === id);

  if (!engineer) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Link to="/engineers">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 ml-2" />
              العودة للمهندسين
            </Button>
          </Link>
        </div>
        <Card>
          <CardContent className="text-center py-12">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              المهندس غير موجود
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              لم يتم العثور على المهندس المطلوب
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // إحصائيات المهندس
  const engineerFaults = faults.filter(f => f.engineer === engineer.name);
  const completedFaults = engineerFaults.filter(f => f.status === 'تم الإصلاح');
  const inProgressFaults = engineerFaults.filter(f => f.status === 'قيد المعالجة');
  const totalFaults = engineerFaults.length;

  const getStatusColor = (status: string) => {
    switch (status) {
      case "متاح":
        return "bg-green-100 text-green-800 border-green-200";
      case "مشغول":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "في إجازة":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getSpecialtyColor = (specialty: string) => {
    switch (specialty) {
      case "أجهزة ليزر":
        return "bg-purple-100 text-purple-800 border-purple-200";
      case "أجهزة تخسيس":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "عام":
        return "bg-gray-100 text-gray-800 border-gray-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link to="/engineers">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 ml-2" />
              العودة للمهندسين
            </Button>
          </Link>
          <div className="flex items-center gap-4">
            <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center">
              <User className="h-8 w-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                {engineer.name}
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                {engineer.id} • {engineer.specialty}
              </p>
            </div>
          </div>
        </div>
        <div className="flex gap-2">
          <Badge className={`${getSpecialtyColor(engineer.specialty)} border`}>
            {engineer.specialty}
          </Badge>
          <Badge className={`${getStatusColor(engineer.status)} border`}>
            {engineer.status}
          </Badge>
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* معلومات المهندس */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              معلومات شخصية
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-sm">
                <Phone className="h-4 w-4 text-gray-500" />
                <span>{engineer.phone}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Mail className="h-4 w-4 text-gray-500" />
                <span>{engineer.email}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <MapPin className="h-4 w-4 text-gray-500" />
                <span>{engineer.area}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Calendar className="h-4 w-4 text-gray-500" />
                <span>انضم في: {new Date(engineer.joinedAt).toLocaleDateString('ar-EG')}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* إحصائيات الأداء */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              إحصائيات الأداء
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{totalFaults}</div>
                <div className="text-xs text-blue-600">إجمالي الأعطال</div>
              </div>
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{completedFaults.length}</div>
                <div className="text-xs text-green-600">أعطال مكتملة</div>
              </div>
              <div className="text-center p-3 bg-yellow-50 rounded-lg">
                <div className="text-2xl font-bold text-yellow-600">{inProgressFaults.length}</div>
                <div className="text-xs text-yellow-600">قيد المعالجة</div>
              </div>
              <div className="text-center p-3 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">
                  {totalFaults > 0 ? Math.round((completedFaults.length / totalFaults) * 100) : 0}%
                </div>
                <div className="text-xs text-purple-600">معدل الإنجاز</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* الحالة الحالية */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Wrench className="h-5 w-5" />
              الحالة الحالية
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">الحالة</label>
              <div className="mt-1">
                <Badge className={`${getStatusColor(engineer.status)} border`}>
                  {engineer.status}
                </Badge>
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">التخصص</label>
              <div className="mt-1">
                <Badge className={`${getSpecialtyColor(engineer.specialty)} border`}>
                  {engineer.specialty}
                </Badge>
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">منطقة العمل</label>
              <p className="mt-1">{engineer.area}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* الأعطال المسندة */}
      {engineerFaults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Wrench className="h-5 w-5" />
              الأعطال المسندة ({engineerFaults.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {engineerFaults.map((fault) => (
                <div key={fault.id} className="p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h4 className="font-medium">{fault.customerName}</h4>
                        <Badge variant="outline" className="text-xs">
                          {fault.id}
                        </Badge>
                        <Badge 
                          className={`text-xs border ${
                            fault.status === 'تم الإصلاح' 
                              ? 'bg-green-100 text-green-800 border-green-200'
                              : fault.status === 'قيد المعالجة'
                              ? 'bg-blue-100 text-blue-800 border-blue-200'
                              : 'bg-yellow-100 text-yellow-800 border-yellow-200'
                          }`}
                        >
                          {fault.status}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                        {fault.deviceType}
                      </p>
                      <p className="text-xs text-gray-500">
                        📅 {new Date(fault.reportedAt).toLocaleDateString('ar-EG')}
                        {fault.completedAt && (
                          <span> • ✅ {new Date(fault.completedAt).toLocaleDateString('ar-EG')}</span>
                        )}
                      </p>
                    </div>
                    <Link to={`/faults/${fault.id}`}>
                      <Button variant="outline" size="sm">
                        عرض التفاصيل
                      </Button>
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default EngineerDetails;
